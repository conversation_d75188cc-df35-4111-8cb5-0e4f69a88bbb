// src/lib/search/fuzzySearch.ts
import type { ChapterSchema } from '$lib/db/db';
import Fuse, { type IFuseOptions } from 'fuse.js';

const options: IFuseOptions<ChapterSchema> = {
  keys: ['title'],
  threshold: 0.4,
  includeMatches: true, // 关键点：返回匹配位置
};

export interface ChapterSearchResult {
  item: ChapterSchema;
  highlightedTitle: string;
}

/**
 * 执行模糊搜索章节标题并返回带高亮的结果
 */
export function searchChaptersByTitle(
  chapters: ChapterSchema[],
  keyword: string
): ChapterSearchResult[] {
  const fuse = new Fuse(chapters, options);
  const results = fuse.search(keyword);

  return results.map(({ item, matches }) => {
    let highlightedTitle = item.title;
    const match = matches?.find((m) => m.key === 'title');

    if (match && match.indices.length) {
      let marked = '';
      let lastIndex = 0;
      for (const [start, end] of match.indices) {
        marked += highlightedTitle.slice(lastIndex, start);
        marked += `<mark>` + highlightedTitle.slice(start, end + 1) + `</mark>`;
        lastIndex = end + 1;
      }
      marked += highlightedTitle.slice(lastIndex);
      highlightedTitle = marked;
    }

    return { item, highlightedTitle };
  });
}
