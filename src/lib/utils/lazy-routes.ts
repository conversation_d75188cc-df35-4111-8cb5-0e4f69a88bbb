// 懒加载路由配置
import type { ComponentType } from "svelte";

// 路由懒加载函数
export function lazyRoute(importFn: () => Promise<{ default: ComponentType }>) {
  return async () => {
    try {
      const module = await importFn();
      return module.default;
    } catch (error) {
      console.error('Failed to load route component:', error);
      // 返回错误组件
      return (await import('$lib/components/ui/EmptyState.svelte')).default;
    }
  };
}

// 预加载路由
export function preloadRoute(importFn: () => Promise<{ default: ComponentType }>) {
  // 在空闲时间预加载
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    requestIdleCallback(() => {
      importFn().catch(console.error);
    });
  } else {
    // 降级到setTimeout
    setTimeout(() => {
      importFn().catch(console.error);
    }, 100);
  }
}

// 路由配置
export const routes = {
  // 核心路由（立即加载）
  home: () => import('../../routes/+page.svelte'),
  
  // 次要路由（懒加载）
  start: lazyRoute(() => import('../../routes/start/+page.svelte')),
  editor: lazyRoute(() => import('../../routes/editor/+page.svelte')),
  model: lazyRoute(() => import('../../routes/model/+page.svelte')),
  gen: lazyRoute(() => import('../../routes/gen/+page.svelte')),
  
  // 书籍相关路由
  book: lazyRoute(() => import('../../routes/book/+page.svelte')),
  chapter: lazyRoute(() => import('../../routes/chapter/+page.svelte')),
};

// 预加载关键路由
export function preloadCriticalRoutes() {
  if (typeof window !== 'undefined') {
    // 预加载用户可能访问的路由
    preloadRoute(() => import('../../routes/start/+page.svelte'));
    preloadRoute(() => import('../../routes/editor/+page.svelte'));
  }
}

// 基于用户行为的智能预加载
export class IntelligentPreloader {
  private static instance: IntelligentPreloader;
  private preloadedRoutes = new Set<string>();
  private userInteractions = new Map<string, number>();

  static getInstance(): IntelligentPreloader {
    if (!IntelligentPreloader.instance) {
      IntelligentPreloader.instance = new IntelligentPreloader();
    }
    return IntelligentPreloader.instance;
  }

  // 记录用户交互
  recordInteraction(route: string): void {
    const count = this.userInteractions.get(route) || 0;
    this.userInteractions.set(route, count + 1);
    
    // 如果用户经常访问某个路由，预加载它
    if (count >= 2 && !this.preloadedRoutes.has(route)) {
      this.preloadRoute(route);
    }
  }

  // 预加载路由
  private preloadRoute(route: string): void {
    const routeImporter = routes[route as keyof typeof routes];
    if (routeImporter && typeof routeImporter === 'function') {
      this.preloadedRoutes.add(route);
      routeImporter().catch(console.error);
    }
  }

  // 基于鼠标悬停预加载
  setupHoverPreload(): void {
    if (typeof window === 'undefined') return;

    document.addEventListener('mouseover', (event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;
      
      if (link && link.href) {
        const url = new URL(link.href);
        const pathname = url.pathname;
        
        // 提取路由名称
        const routeName = this.extractRouteName(pathname);
        if (routeName && !this.preloadedRoutes.has(routeName)) {
          // 延迟预加载，避免误触
          setTimeout(() => {
            this.preloadRoute(routeName);
          }, 100);
        }
      }
    });
  }

  private extractRouteName(pathname: string): string | null {
    const segments = pathname.split('/').filter(Boolean);
    if (segments.length === 0) return 'home';
    return segments[0];
  }
}

// 资源优先级管理
export class ResourcePriorityManager {
  private static instance: ResourcePriorityManager;
  private criticalResources = new Set<string>();
  private deferredResources = new Set<string>();

  static getInstance(): ResourcePriorityManager {
    if (!ResourcePriorityManager.instance) {
      ResourcePriorityManager.instance = new ResourcePriorityManager();
    }
    return ResourcePriorityManager.instance;
  }

  // 标记关键资源
  markAsCritical(resource: string): void {
    this.criticalResources.add(resource);
  }

  // 标记延迟资源
  markAsDeferred(resource: string): void {
    this.deferredResources.add(resource);
  }

  // 加载关键资源
  loadCriticalResources(): Promise<void[]> {
    const promises = Array.from(this.criticalResources).map(resource => {
      return this.loadResource(resource, 'high');
    });
    
    return Promise.all(promises);
  }

  // 延迟加载非关键资源
  loadDeferredResources(): void {
    if (typeof window === 'undefined') return;

    // 在页面加载完成后加载延迟资源
    window.addEventListener('load', () => {
      requestIdleCallback(() => {
        this.deferredResources.forEach(resource => {
          this.loadResource(resource, 'low').catch(console.error);
        });
      });
    });
  }

  private async loadResource(resource: string, priority: 'high' | 'low'): Promise<void> {
    try {
      if (resource.endsWith('.js') || resource.endsWith('.ts')) {
        await import(resource);
      } else if (resource.endsWith('.css')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = resource;
        link.fetchPriority = priority;
        document.head.appendChild(link);
      }
    } catch (error) {
      console.error(`Failed to load resource: ${resource}`, error);
    }
  }
}

// 初始化性能优化
export function initPerformanceOptimizations(): void {
  if (typeof window === 'undefined') return;

  // 预加载关键路由
  preloadCriticalRoutes();

  // 设置智能预加载
  const preloader = IntelligentPreloader.getInstance();
  preloader.setupHoverPreload();

  // 设置资源优先级管理
  const resourceManager = ResourcePriorityManager.getInstance();
  
  // 标记关键资源
  resourceManager.markAsCritical('/src/lib/components/ui/index.ts');
  resourceManager.markAsCritical('/src/lib/services/data.service.ts');
  
  // 标记延迟资源
  resourceManager.markAsDeferred('/src/lib/services/ai.service.ts');
  resourceManager.markAsDeferred('/src/lib/services/prompt.service.ts');
  
  // 加载关键资源
  resourceManager.loadCriticalResources().catch(console.error);
  
  // 延迟加载非关键资源
  resourceManager.loadDeferredResources();
}
