// 缓存管理系统

export interface CacheOptions {
  ttl?: number; // 生存时间（毫秒）
  maxSize?: number; // 最大缓存条目数
  serialize?: boolean; // 是否序列化存储
}

export interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

// 内存缓存
export class MemoryCache<T = any> {
  private cache = new Map<string, CacheEntry<T>>();
  private options: Required<CacheOptions>;

  constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: options.ttl || 5 * 60 * 1000, // 默认5分钟
      maxSize: options.maxSize || 100,
      serialize: options.serialize || false
    };
  }

  set(key: string, value: T, ttl?: number): void {
    // 如果缓存已满，删除最少使用的条目
    if (this.cache.size >= this.options.maxSize) {
      this.evictLeastUsed();
    }

    const entry: CacheEntry<T> = {
      value: this.options.serialize ? JSON.parse(JSON.stringify(value)) : value,
      timestamp: Date.now(),
      ttl: ttl || this.options.ttl,
      accessCount: 0,
      lastAccessed: Date.now()
    };

    this.cache.set(key, entry);
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问统计
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    return entry.value;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // 获取缓存统计
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    entries: Array<{ key: string; accessCount: number; age: number }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      accessCount: entry.accessCount,
      age: Date.now() - entry.timestamp
    }));

    const totalAccess = entries.reduce((sum, entry) => sum + entry.accessCount, 0);
    const hitRate = totalAccess > 0 ? (totalAccess / (totalAccess + this.cache.size)) * 100 : 0;

    return {
      size: this.cache.size,
      maxSize: this.options.maxSize,
      hitRate,
      entries
    };
  }

  // 清理过期条目
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    return cleaned;
  }

  private evictLeastUsed(): void {
    let leastUsedKey: string | null = null;
    let leastUsedCount = Infinity;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessCount < leastUsedCount || 
          (entry.accessCount === leastUsedCount && entry.lastAccessed < oldestTime)) {
        leastUsedKey = key;
        leastUsedCount = entry.accessCount;
        oldestTime = entry.lastAccessed;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
    }
  }
}

// LocalStorage缓存
export class LocalStorageCache<T = any> {
  private prefix: string;
  private options: Required<CacheOptions>;

  constructor(prefix: string = 'cache_', options: CacheOptions = {}) {
    this.prefix = prefix;
    this.options = {
      ttl: options.ttl || 24 * 60 * 60 * 1000, // 默认24小时
      maxSize: options.maxSize || 50,
      serialize: true // LocalStorage总是需要序列化
    };
  }

  set(key: string, value: T, ttl?: number): void {
    if (typeof window === 'undefined') return;

    try {
      const entry: CacheEntry<T> = {
        value,
        timestamp: Date.now(),
        ttl: ttl || this.options.ttl,
        accessCount: 0,
        lastAccessed: Date.now()
      };

      localStorage.setItem(this.prefix + key, JSON.stringify(entry));
      
      // 检查缓存大小限制
      this.enforceMaxSize();
    } catch (error) {
      console.warn('Failed to set localStorage cache:', error);
    }
  }

  get(key: string): T | null {
    if (typeof window === 'undefined') return null;

    try {
      const item = localStorage.getItem(this.prefix + key);
      if (!item) return null;

      const entry: CacheEntry<T> = JSON.parse(item);
      
      // 检查是否过期
      if (Date.now() - entry.timestamp > entry.ttl) {
        localStorage.removeItem(this.prefix + key);
        return null;
      }

      // 更新访问统计
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      localStorage.setItem(this.prefix + key, JSON.stringify(entry));

      return entry.value;
    } catch (error) {
      console.warn('Failed to get localStorage cache:', error);
      return null;
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    if (typeof window === 'undefined') return false;
    
    try {
      localStorage.removeItem(this.prefix + key);
      return true;
    } catch (error) {
      console.warn('Failed to delete localStorage cache:', error);
      return false;
    }
  }

  clear(): void {
    if (typeof window === 'undefined') return;

    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
      keys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.warn('Failed to clear localStorage cache:', error);
    }
  }

  // 清理过期条目
  cleanup(): number {
    if (typeof window === 'undefined') return 0;

    let cleaned = 0;
    const now = Date.now();

    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
      
      for (const key of keys) {
        const item = localStorage.getItem(key);
        if (item) {
          const entry: CacheEntry<any> = JSON.parse(item);
          if (now - entry.timestamp > entry.ttl) {
            localStorage.removeItem(key);
            cleaned++;
          }
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup localStorage cache:', error);
    }

    return cleaned;
  }

  private enforceMaxSize(): void {
    if (typeof window === 'undefined') return;

    try {
      const keys = Object.keys(localStorage)
        .filter(key => key.startsWith(this.prefix))
        .map(key => {
          const item = localStorage.getItem(key);
          if (item) {
            const entry: CacheEntry<any> = JSON.parse(item);
            return { key, lastAccessed: entry.lastAccessed, accessCount: entry.accessCount };
          }
          return null;
        })
        .filter(Boolean) as Array<{ key: string; lastAccessed: number; accessCount: number }>;

      if (keys.length > this.options.maxSize) {
        // 按访问频率和时间排序，删除最少使用的
        keys.sort((a, b) => {
          if (a.accessCount !== b.accessCount) {
            return a.accessCount - b.accessCount;
          }
          return a.lastAccessed - b.lastAccessed;
        });

        const toDelete = keys.slice(0, keys.length - this.options.maxSize);
        toDelete.forEach(item => localStorage.removeItem(item.key));
      }
    } catch (error) {
      console.warn('Failed to enforce localStorage cache max size:', error);
    }
  }
}

// 缓存管理器
export class CacheManager {
  private static instance: CacheManager;
  private memoryCache = new MemoryCache();
  private localStorageCache = new LocalStorageCache();

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  // 设置缓存（自动选择存储方式）
  set(key: string, value: any, options?: { ttl?: number; persistent?: boolean }): void {
    const { ttl, persistent = false } = options || {};
    
    if (persistent) {
      this.localStorageCache.set(key, value, ttl);
    } else {
      this.memoryCache.set(key, value, ttl);
    }
  }

  // 获取缓存（从内存和localStorage中查找）
  get(key: string): any {
    // 先从内存缓存查找
    let value = this.memoryCache.get(key);
    if (value !== null) {
      return value;
    }

    // 再从localStorage查找
    value = this.localStorageCache.get(key);
    if (value !== null) {
      // 将热数据提升到内存缓存
      this.memoryCache.set(key, value);
      return value;
    }

    return null;
  }

  // 删除缓存
  delete(key: string): void {
    this.memoryCache.delete(key);
    this.localStorageCache.delete(key);
  }

  // 清理所有缓存
  clear(): void {
    this.memoryCache.clear();
    this.localStorageCache.clear();
  }

  // 定期清理过期缓存
  startPeriodicCleanup(interval: number = 5 * 60 * 1000): void {
    setInterval(() => {
      const memoryCleanedCount = this.memoryCache.cleanup();
      const localStorageCleanedCount = this.localStorageCache.cleanup();
      
      if (memoryCleanedCount > 0 || localStorageCleanedCount > 0) {
        console.log(`Cache cleanup: ${memoryCleanedCount} memory entries, ${localStorageCleanedCount} localStorage entries`);
      }
    }, interval);
  }

  // 获取缓存统计
  getStats(): {
    memory: ReturnType<MemoryCache['getStats']>;
    localStorage: { size: number };
  } {
    const localStorageSize = typeof window !== 'undefined' 
      ? Object.keys(localStorage).filter(key => key.startsWith('cache_')).length 
      : 0;

    return {
      memory: this.memoryCache.getStats(),
      localStorage: { size: localStorageSize }
    };
  }
}

// 全局缓存实例
export const globalCache = CacheManager.getInstance();
