// 性能监控工具

export interface PerformanceMetrics {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private observers: Array<(metrics: PerformanceMetrics) => void> = [];

  // 开始计时
  start(name: string, metadata?: Record<string, any>): void {
    const startTime = performance.now();
    this.metrics.set(name, {
      name,
      startTime,
      metadata
    });
  }

  // 结束计时
  end(name: string): PerformanceMetrics | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    const completedMetric: PerformanceMetrics = {
      ...metric,
      endTime,
      duration
    };

    this.metrics.set(name, completedMetric);
    
    // 通知观察者
    this.observers.forEach(observer => observer(completedMetric));

    return completedMetric;
  }

  // 测量函数执行时间
  async measure<T>(name: string, fn: () => T | Promise<T>, metadata?: Record<string, any>): Promise<T> {
    this.start(name, metadata);
    try {
      const result = await fn();
      this.end(name);
      return result;
    } catch (error) {
      this.end(name);
      throw error;
    }
  }

  // 获取指标
  getMetric(name: string): PerformanceMetrics | undefined {
    return this.metrics.get(name);
  }

  // 获取所有指标
  getAllMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }

  // 清除指标
  clear(): void {
    this.metrics.clear();
  }

  // 添加观察者
  addObserver(observer: (metrics: PerformanceMetrics) => void): void {
    this.observers.push(observer);
  }

  // 移除观察者
  removeObserver(observer: (metrics: PerformanceMetrics) => void): void {
    const index = this.observers.indexOf(observer);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  // 生成性能报告
  generateReport(): {
    totalMetrics: number;
    averageDuration: number;
    slowestOperations: PerformanceMetrics[];
    fastestOperations: PerformanceMetrics[];
  } {
    const completedMetrics = this.getAllMetrics().filter(m => m.duration !== undefined);
    
    if (completedMetrics.length === 0) {
      return {
        totalMetrics: 0,
        averageDuration: 0,
        slowestOperations: [],
        fastestOperations: []
      };
    }

    const totalDuration = completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);
    const averageDuration = totalDuration / completedMetrics.length;

    const sortedByDuration = [...completedMetrics].sort((a, b) => (b.duration || 0) - (a.duration || 0));

    return {
      totalMetrics: completedMetrics.length,
      averageDuration,
      slowestOperations: sortedByDuration.slice(0, 5),
      fastestOperations: sortedByDuration.slice(-5).reverse()
    };
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 装饰器函数，用于自动测量函数性能
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const metricName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      return performanceMonitor.measure(metricName, () => originalMethod.apply(this, args));
    };

    return descriptor;
  };
}

// 内存使用监控
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private intervalId: number | null = null;
  private callbacks: Array<(info: any) => void> = [];

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  // 开始监控内存使用
  startMonitoring(interval: number = 5000): void {
    if (this.intervalId) {
      this.stopMonitoring();
    }

    this.intervalId = window.setInterval(() => {
      const memoryInfo = this.getMemoryInfo();
      this.callbacks.forEach(callback => callback(memoryInfo));
    }, interval);
  }

  // 停止监控
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  // 获取内存信息
  getMemoryInfo(): any {
    if ('memory' in performance) {
      return {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
        timestamp: Date.now()
      };
    }
    return null;
  }

  // 添加回调
  addCallback(callback: (info: any) => void): void {
    this.callbacks.push(callback);
  }

  // 移除回调
  removeCallback(callback: (info: any) => void): void {
    const index = this.callbacks.indexOf(callback);
    if (index > -1) {
      this.callbacks.splice(index, 1);
    }
  }
}

// 网络性能监控
export class NetworkMonitor {
  private static instance: NetworkMonitor;
  private requests: Map<string, any> = new Map();

  static getInstance(): NetworkMonitor {
    if (!NetworkMonitor.instance) {
      NetworkMonitor.instance = new NetworkMonitor();
    }
    return NetworkMonitor.instance;
  }

  // 监控fetch请求
  monitorFetch(): void {
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      const startTime = performance.now();
      
      try {
        const response = await originalFetch(input, init);
        const endTime = performance.now();
        
        this.recordRequest({
          url,
          method: init?.method || 'GET',
          status: response.status,
          duration: endTime - startTime,
          success: response.ok,
          timestamp: Date.now()
        });
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        
        this.recordRequest({
          url,
          method: init?.method || 'GET',
          status: 0,
          duration: endTime - startTime,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now()
        });
        
        throw error;
      }
    };
  }

  private recordRequest(requestInfo: any): void {
    const id = `${requestInfo.method}_${requestInfo.url}_${requestInfo.timestamp}`;
    this.requests.set(id, requestInfo);
    
    // 保持最近100个请求
    if (this.requests.size > 100) {
      const firstKey = this.requests.keys().next().value;
      this.requests.delete(firstKey);
    }
  }

  // 获取网络统计
  getNetworkStats(): {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    slowestRequests: any[];
  } {
    const requests = Array.from(this.requests.values());
    
    if (requests.length === 0) {
      return {
        totalRequests: 0,
        successRate: 0,
        averageResponseTime: 0,
        slowestRequests: []
      };
    }

    const successfulRequests = requests.filter(r => r.success);
    const totalDuration = requests.reduce((sum, r) => sum + r.duration, 0);
    const sortedByDuration = [...requests].sort((a, b) => b.duration - a.duration);

    return {
      totalRequests: requests.length,
      successRate: (successfulRequests.length / requests.length) * 100,
      averageResponseTime: totalDuration / requests.length,
      slowestRequests: sortedByDuration.slice(0, 5)
    };
  }
}

// 初始化性能监控
export function initPerformanceMonitoring(): void {
  // 监控页面加载性能
  if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        performanceMonitor.start('page_load');
        performanceMonitor.end('page_load');
        
        console.log('Page Load Performance:', {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalTime: navigation.loadEventEnd - navigation.fetchStart
        });
      }, 0);
    });

    // 启动网络监控
    NetworkMonitor.getInstance().monitorFetch();
  }
}
