<script lang="ts">
  import { responseText } from "$lib/stores/response.store";
  import { isLoading } from "$lib/models/app-state.model";
</script>

<div class="space-y-6">
  <h1 class="text-3xl font-bold text-gray-800">AI 回应</h1>

  <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
    <div class="p-6 h-full">
      <div class="whitespace-pre-wrap text-gray-800 min-h-[500px]">
        {#if $responseText}
          <p>{$responseText}</p>
        {:else}
          <div class="text-gray-400 h-full flex items-center justify-center">
            {#if $isLoading}
              <div class="flex items-center gap-2">
                <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>正在获取响应...</span>
              </div>
            {:else}
              <p>等待响应内容...</p>
            {/if}
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>