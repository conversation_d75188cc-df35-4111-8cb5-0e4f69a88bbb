<script lang="ts">
  import {
    showConfigModal,
    geminiConfig,
    errors,
  } from "$lib/models/app-state.model";
  import { validateConfig } from "$lib/services/gemini.service";
  import { scale } from "svelte/transition";

  let localConfig = { ...$geminiConfig };

  $: {
    localConfig = { ...$geminiConfig };
  }

  function closeModal() {
    $showConfigModal = false;
    $errors = {};
  }

  function handleOutsideClick(e: MouseEvent) {
    if (e.target === e.currentTarget) {
      closeModal();
    }
  }

  function saveConfig() {
    const validationErrors = validateConfig(localConfig);

    if (Object.keys(validationErrors).length > 0) {
      $errors = validationErrors;
      return;
    }

    $geminiConfig = localConfig;
    closeModal();
  }

  function resetConfig() {
    localConfig = {
      apiKey: "",
      modelName: "gemini-2.0-flash-lite-preview-02-05",
      temperature: 0.6,
      maxOutputTokens: 6000,
    };
    $errors = {};
  }
</script>

{#if $showConfigModal}
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
    on:click={handleOutsideClick}
    on:keydown|stopPropagation
  >
    <div
      class="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
    >
      <div class="p-6 space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-2xl font-bold text-gray-800">Gemini 配置</h2>
          <button
            on:click={closeModal}
            class="text-gray-500 hover:text-gray-700 text-2xl"
          >
            &times;
          </button>
        </div>

        <div class="space-y-4">
          <div>
            <label
              class="block text-sm font-medium text-gray-700 mb-1"
              for="apiKey">API key</label
            >
            <input
              id="apiKey"
              bind:value={localConfig.apiKey}
              class="w-full px-4 py-3 border {$errors.apiKey
                ? 'border-red-500'
                : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入您的API密钥"
            />
            {#if $errors.apiKey}
              <p class="mt-1 text-sm text-red-600">{$errors.apiKey}</p>
            {/if}
          </div>

          <div>
            <label
              class="block text-sm font-medium text-gray-700 mb-1"
              for="modelName">模型名称</label
            >
            <input
              id="modelName"
              bind:value={localConfig.modelName}
              class="w-full px-4 py-3 border {$errors.modelName
                ? 'border-red-500'
                : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="选择模型"
            />
            {#if $errors.modelName}
              <p class="mt-1 text-sm text-red-600">{$errors.modelName}</p>
            {/if}
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="modelName">随机性 (0-1)</label
              >
              <input
                id="modelName"
                type="number"
                min="0"
                max="1"
                step="0.1"
                bind:value={localConfig.temperature}
                class="w-full px-4 py-3 border {$errors.temperature
                  ? 'border-red-500'
                  : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              {#if $errors.temperature}
                <p class="mt-1 text-sm text-red-600">{$errors.temperature}</p>
              {/if}
            </div>

            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="maxOutputTokens">最大Token长度</label
              >
              <input
                id="maxOutputTokens"
                type="number"
                min="1"
                bind:value={localConfig.maxOutputTokens}
                class="w-full px-4 py-3 border {$errors.maxOutputTokens
                  ? 'border-red-500'
                  : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              {#if $errors.maxOutputTokens}
                <p class="mt-1 text-sm text-red-600">
                  {$errors.maxOutputTokens}
                </p>
              {/if}
            </div>
          </div>
        </div>

        <div class="flex justify-between pt-4">
          <button
            class="px-4 py-2 text-gray-600 hover:text-gray-800"
            on:click={resetConfig}
          >
            重置为默认
          </button>

          <div class="flex space-x-3">
            <button
              class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              on:click={closeModal}
            >
              取消
            </button>
            <button
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              on:click={saveConfig}
            >
              保存配置
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
{/if}
