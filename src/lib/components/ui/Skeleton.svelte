<script lang="ts">
  import type { HTMLAttributes } from "svelte/elements";

  interface Props extends HTMLAttributes<HTMLDivElement> {
    variant?: "text" | "circular" | "rectangular";
    width?: string;
    height?: string;
    lines?: number | string;
    children?: any;
  }

  let {
    variant = "rectangular",
    width = "100%",
    height = "1rem",
    lines = 1,
    class: className = "",
    children,
    ...restProps
  }: Props = $props();

  // 基础样式
  const baseClasses = "animate-pulse bg-gray-300 rounded";

  // 变体样式
  const variantClasses = {
    text: "rounded",
    circular: "rounded-full",
    rectangular: "rounded"
  };

  // 组合样式
  $: computedClasses = [
    baseClasses,
    variantClasses[variant],
    className
  ].filter(Boolean).join(" ");

  // 样式对象
  $: style = `width: ${width}; height: ${height};`;
</script>

{#if variant === "text" && lines > 1}
  <div class="space-y-2">
    {#each Array(lines) as _, i}
      <div
        class={computedClasses}
        style={`width: ${i === lines - 1 ? '75%' : '100%'}; height: ${height};`}
        {...restProps}
      ></div>
    {/each}
  </div>
{:else}
  <div
    class={computedClasses}
    {style}
    {...restProps}
  >
    {@render children?.()}
  </div>
{/if}
