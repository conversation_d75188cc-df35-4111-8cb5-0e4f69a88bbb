<script lang="ts">
  interface Props {
    type?: "text" | "email" | "password" | "number" | "tel" | "url" | "search";
    value?: string | number;
    placeholder?: string;
    disabled?: boolean;
    readonly?: boolean;
    required?: boolean;
    label?: string;
    error?: string;
    hint?: string;
    size?: "sm" | "md" | "lg";
    fullWidth?: boolean;
    class?: string;
    id?: string;
    name?: string;
    autocomplete?: string;
    oninput?: (event: Event) => void;
    onchange?: (event: Event) => void;
    onfocus?: (event: FocusEvent) => void;
    onblur?: (event: FocusEvent) => void;
  }

  let {
    type = "text",
    value = $bindable(),
    placeholder,
    disabled = false,
    readonly = false,
    required = false,
    label,
    error,
    hint,
    size = "md",
    fullWidth = false,
    class: className = "",
    id,
    name,
    autocomplete,
    oninput,
    onchange,
    onfocus,
    onblur
  }: Props = $props();

  // 基础样式
  const baseClasses = "border rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed";

  // 尺寸样式
  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-4 py-3 text-lg"
  };

  // 状态样式
  $: stateClasses = error 
    ? "border-red-300 focus:border-red-500 focus:ring-red-500" 
    : "border-gray-300 hover:border-gray-400";

  // 组合样式
  $: computedClasses = [
    baseClasses,
    sizeClasses[size],
    stateClasses,
    fullWidth ? "w-full" : "",
    className
  ].filter(Boolean).join(" ");

  // 生成唯一ID
  $: inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
</script>

<div class={fullWidth ? "w-full" : ""}>
  {#if label}
    <label for={inputId} class="block text-sm font-medium text-gray-700 mb-1">
      {label}
      {#if required}
        <span class="text-red-500">*</span>
      {/if}
    </label>
  {/if}
  
  <input
    {type}
    bind:value
    {placeholder}
    {disabled}
    {readonly}
    {required}
    {name}
    {autocomplete}
    id={inputId}
    class={computedClasses}
    {oninput}
    {onchange}
    {onfocus}
    {onblur}
  />
  
  {#if error}
    <p class="mt-1 text-sm text-red-600">{error}</p>
  {:else if hint}
    <p class="mt-1 text-sm text-gray-500">{hint}</p>
  {/if}
</div>
