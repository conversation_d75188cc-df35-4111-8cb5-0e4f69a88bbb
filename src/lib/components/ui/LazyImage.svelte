<script lang="ts">
  import { onMount } from "svelte";

  interface Props {
    src: string;
    alt: string;
    placeholder?: string;
    class?: string;
    width?: number | string;
    height?: number | string;
    threshold?: number;
    rootMargin?: string;
  }

  let {
    src,
    alt,
    placeholder = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300'%3E%3Crect width='100%25' height='100%25' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23d1d5db'%3E加载中...%3C/text%3E%3C/svg%3E",
    class: className = "",
    width,
    height,
    threshold = 0.1,
    rootMargin = "50px"
  }: Props = $props();

  let imgElement: HTMLImageElement;
  let isLoaded = false;
  let isError = false;
  let currentSrc = placeholder;

  onMount(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            loadImage();
            observer.unobserve(imgElement);
          }
        });
      },
      {
        threshold,
        rootMargin
      }
    );

    if (imgElement) {
      observer.observe(imgElement);
    }

    return () => {
      if (imgElement) {
        observer.unobserve(imgElement);
      }
    };
  });

  function loadImage() {
    const img = new Image();
    
    img.onload = () => {
      currentSrc = src;
      isLoaded = true;
      isError = false;
    };
    
    img.onerror = () => {
      isError = true;
      isLoaded = false;
    };
    
    img.src = src;
  }

  function handleImageLoad() {
    isLoaded = true;
  }

  function handleImageError() {
    isError = true;
    currentSrc = placeholder;
  }
</script>

<div class="lazy-image-container {className}">
  <img
    bind:this={imgElement}
    src={currentSrc}
    {alt}
    {width}
    {height}
    class="lazy-image"
    class:loaded={isLoaded}
    class:error={isError}
    on:load={handleImageLoad}
    on:error={handleImageError}
  />
  
  {#if !isLoaded && !isError}
    <div class="lazy-image-overlay">
      <div class="lazy-image-spinner">
        <svg class="animate-spin h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    </div>
  {/if}
  
  {#if isError}
    <div class="lazy-image-error">
      <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
      </svg>
      <span class="text-sm text-gray-500 mt-2">加载失败</span>
    </div>
  {/if}
</div>

<style>
  .lazy-image-container {
    position: relative;
    display: inline-block;
    overflow: hidden;
  }

  .lazy-image {
    transition: opacity 0.3s ease;
    opacity: 0.7;
  }

  .lazy-image.loaded {
    opacity: 1;
  }

  .lazy-image.error {
    opacity: 0.3;
  }

  .lazy-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
  }

  .lazy-image-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(249, 250, 251, 0.9);
  }
</style>
