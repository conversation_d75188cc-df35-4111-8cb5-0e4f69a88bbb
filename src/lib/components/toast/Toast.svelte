<!-- src/lib/components/Toast.svelte -->
<script lang="ts">
  import { fly } from "svelte/transition";
  import toasts, { removeToast } from "./toastStore";

  const typeClasses = {
    success: "bg-green-500",
    error: "bg-red-500",
    info: "bg-blue-500",
    warning: "bg-yellow-500 text-black",
  };
</script>

<div class="fixed top-4 right-4 z-50">
  <div class="flex flex-col space-y-2">
    {#each $toasts as toast (toast.id)}
      <button
        in:fly={{ x: 200, duration: 300 }}
        out:fly={{ x: 200, duration: 300 }}
        class={`rounded shadow-lg px-4 py-2 text-white ${typeClasses[toast.type]} cursor-pointer`}
        on:click={() => removeToast(toast.id)}
      >
        {toast.message}
      </button>
    {/each}
  </div>
</div>
