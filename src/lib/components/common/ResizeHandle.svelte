<script lang="ts">
  export let direction: "horizontal" | "vertical" = "horizontal";
  export let onResize: (delta: number) => void;

  let isDragging = false;
  let startPosition = 0;

  const handleMouseDown = (e: MouseEvent) => {
    isDragging = true;
    startPosition = direction === "vertical" ? e.clientX : e.clientY;
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const currentPosition = direction === "vertical" ? e.clientX : e.clientY;
    const delta = currentPosition - startPosition;
    startPosition = currentPosition;

    onResize(delta);
  };

  const handleMouseUp = () => {
    if (!isDragging) return;
    isDragging = false;
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
  };
</script>

<!-- svelte-ignore a11y-no-static-element-interactions -->
<!-- svelte-ignore element_invalid_self_closing_tag -->
<div
  class="bg-gray-200 dark:bg-gray-700 hover:bg-blue-500 active:bg-blue-600 {direction ===
  'vertical'
    ? 'cursor-col-resize w-1 h-full'
    : 'cursor-row-resize h-1 w-full'}"
  on:mousedown={handleMouseDown}
/>
