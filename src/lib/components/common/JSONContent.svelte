<script lang="ts">
  import { autogrow } from "$lib/actions/autogrow";

  export let showToolbar = true;
  export let isShow = false;
  export let name = "download";
  export let obj: any;
  export let placeholder = "";

  let json = "";
  let copyButtonText = "Copy";

  $: {
    try {
      if (!obj) {
        json = "";
      } else {
        json = JSON.stringify(obj, null, 2);
      }
    } catch (error: any) {
      json = error.message;
    }
  }

  function handleInput(e: any) {
    try {
      obj = JSON.parse(e.target?.value);
    } catch (error: any) {
      console.log("handleInput", error.message);
    }
  }

  function handleCopy() {
    navigator.clipboard
      .writeText(json)
      .then(() => {
        copyButtonText = "Copied!";
      })
      .catch(() => {
        copyButtonText = "Failed";
      })
      .finally(() => {
        setTimeout(() => {
          copyButtonText = "Copy";
        }, 2000);
      });
  }
</script>

{#if isShow}
  <div class="bg-gray-200 px-2 py-1 rounded-lg">
    {#if showToolbar}
      <div class="flex justify-between items-center gap-3">
        <span>JSON</span><span class="flex-1"></span>
        <button
          class="text-gray-600 hover:text-black"
          on:click={() => {
            isShow = false;
          }}
          >收起
        </button>
        <button class="text-gray-600 hover:text-black" on:click={handleCopy}
          >{copyButtonText}
        </button>
        <button
          class="text-gray-600 hover:text-black"
          on:click={() => {
            downloadJSONFile(name, obj);
          }}
          >下载
        </button>
      </div>
    {/if}
    <textarea
      use:autogrow={json}
      bind:value={json}
      on:input={handleInput}
      {placeholder}
      rows="7"
      class="w-full min-h-48 overflow-y-hidden resize-none p-1 placeholder:text-gray-400"
    ></textarea>
  </div>
{/if}
