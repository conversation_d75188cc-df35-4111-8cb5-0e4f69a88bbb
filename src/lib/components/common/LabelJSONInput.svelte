<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import JsonContent from "./JSONContent.svelte";

  // 组件属性
  export let multiple = true; // 是否多选，默认为true
  export let placeholder = "自定义对象 (JSON格式)";
  export let title = "";
  export let selected: any | any[]; // 当前选中的值

  const dispatcher = createEventDispatcher();

  function handleAddItem() {
    let item: any = "";
    if (multiple) {
      if (Array.isArray(selected)) {
        selected = [...selected, item];
      } else {
        selected = [item];
      }
    } else {
      selected = item;
    }
    dispatcher("changed");
  }
</script>

<div class="flex flex-col gap-2 {$$props.class}">
  {#if multiple}
    {#each selected as _, i}
      <JsonContent
        bind:obj={selected[i]}
        name={title + "_" + i}
        isShow={true}
        showToolbar={false}
        {placeholder}
      ></JsonContent>
    {/each}
    <button
      class="mt-3 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-light"
      on:click={handleAddItem}>再添加一个{title}</button
    >
  {:else}
    <JsonContent
      bind:obj={selected}
      name={title}
      isShow={true}
      showToolbar={false}
      {placeholder}
    ></JsonContent>
  {/if}
</div>
