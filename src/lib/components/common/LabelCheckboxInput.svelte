<script lang="ts">
  import { createEventDispatcher } from "svelte";

  // 组件属性
  export let values: any[] = []; // 标签选项数组
  export let selected: any | any[]; // 当前选中的值
  export let multiple = true; // 是否多选，默认为true
  export let disabled = false; // 是否禁用
  export let label: (item: any) => string = (item) => String(item);
  export let compare: (a: any, b: any) => boolean = (a, b) => a === b;

  const dispatcher = createEventDispatcher();

  // 处理标签点击
  function handleTagClick(value: any) {
    if (disabled) return;

    if (multiple) {
      if (!Array.isArray(selected)) {
        selected = [];
      }
      if (Array.isArray(selected)) {
        // 多选模式
        if (isSelected(value)) {
          // 如果已选中，则取消选择
          selected = selected.filter((item) => !compare(item, value));
        } else {
          // 如果未选中，则添加到选择列表
          selected = [...selected, value];
        }
      }
    } else {
      // 单选模式
      if (selected === value) {
        // 如果已选中，则取消选择（可选行为）
        selected = "";
      } else {
        // 如果未选中，则设置为当前选择
        selected = value;
      }
    }

    dispatcher("changed");
  }

  // 判断标签是否被选中
  function isSelected(value: any) {
    if (Array.isArray(selected)) {
      return -1 != selected.findIndex((item) => compare(item, value));
    }
    return compare(selected, value);
  }

  // 获取标签样式类
  function getTagClasses(value: string) {
    const baseClasses =
      "inline-flex items-center px-4 py-1 rounded-full text-base transition-colors cursor-pointer";
    const disabledClasses = disabled ? "opacity-50 cursor-not-allowed" : "";

    if (isSelected(value)) {
      return `${baseClasses} bg-indigo-600 text-white hover:bg-indigo-700 ${disabledClasses}`;
    } else {
      return `${baseClasses} bg-indigo-100 hover:bg-indigo-200 text-indigo-800 ${disabledClasses}`;
    }
  }
</script>

<div class="flex flex-wrap gap-2 {$$props.class}">
  {#each values as value}
    <button
      class={getTagClasses(value)}
      on:click={() => handleTagClick(value)}
      {disabled}
      aria-pressed={isSelected(value)}
      type="button"
    >
      {label(value)}
      {#if isSelected(value)}
        <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          />
        </svg>
      {/if}
    </button>
  {/each}
  <slot />
</div>
