<script lang="ts">
  import { createEventDispatcher } from "svelte";

  // 组件属性
  export let multiple = true; // 是否多选，默认为true
  export let placeholder = "";
  export let title = "";
  export let type;
  export let selected: any | any[]; // 当前选中的值

  const dispatcher = createEventDispatcher();

  function handleAddItem() {
    let item: any;
    if (multiple) {
      if (Array.isArray(selected)) {
        selected = [...selected, item];
      } else {
        selected = [item];
      }
    } else {
      selected = item;
    }
    dispatcher("changed");
  }
</script>

<div class="flex flex-wrap gap-2 {$$props.class}">
  {#if multiple}
    {#each selected as _, i}
      <input
        bind:value={selected[i]}
        class="w-full align-top overflow-y-hidden resize-none font-light text-lg text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors py-1"
        {type}
        {placeholder}
        disabled={false}
      />
    {/each}
    <button
      class="mt-3 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-light"
      on:click={handleAddItem}>再添加一个{title}</button
    >
  {:else}
    <input
      bind:value={selected}
      class="w-full align-top overflow-y-hidden resize-none font-light text-lg text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors py-1"
      {type}
      {placeholder}
      disabled={false}
    />
  {/if}
</div>
