<script>
  import { onMount } from "svelte";
  import ActivityBarIcon from "./ActivityBarIcon.svelte";

  // 主题相关
  let darkMode = true;

  // 检测系统主题偏好
  function detectColorScheme() {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia(
        "(prefers-color-scheme: dark)",
      ).matches;
      darkMode = prefersDark;
      document.documentElement.classList.toggle("dark", prefersDark);
    }
  }

  // 切换主题
  function toggleTheme() {
    darkMode = !darkMode;
    document.documentElement.classList.toggle("dark", darkMode);
  }

  // 初始化
  onMount(() => {
    detectColorScheme();

    // 监听系统主题变化
    if (typeof window !== "undefined") {
      window
        .matchMedia("(prefers-color-scheme: dark)")
        .addEventListener("change", (e) => {
          darkMode = e.matches;
          document.documentElement.classList.toggle("dark", e.matches);
        });
    }
  });
</script>

<button
  class="p-2 my-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 w-full flex justify-center"
  title="Toggle Theme"
  on:click={toggleTheme}
>
  <ActivityBarIcon name={darkMode ? "moon" : "sun"} />
</button>
