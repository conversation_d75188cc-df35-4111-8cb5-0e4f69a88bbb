
export const NovelGuidePrompt = `export interface GeneratedInterface {
  key: string; // 变量名
  name: string; // 变量名称（中文名）
  desc: string; // 变量描述
  value: "boolean" | "number" | "string" | "array" | "json" | string[]; // 该变量的类型，当 value 的值为 string[] 时，则表示该元素为枚举类型，当 fields 有值时，则忽略 value 的状态。
  fields: GeneratedInterface[]; // 该变量是一个复杂数据结构
  isArray: boolean; // 该变量是否为数组
}
---------
回复内容请务必使用以上(GeneratedInterface)类型返回格式化的 json 数据，数据尽量扁平化，如果 value 为 string[] 时，请尽可能多的列出值；当变量有多个字段时，请使用复杂数据结构。
请在回复前，仔细思考用户的需求，并根据专业领域的知识和现代前沿理论给出答案，以及分析如何将结果完美的转换为以上类型的数据并给出回复。`;

export const CreateBookPrompt = `/** 小说书籍信息 */
export interface BookSchema {
  title: string;
  summary: string;
}
---------
回复内容请务必使用以上(BookSchema)类型返回格式化的 json 数据。请根据提供的标题或简介进行适度的扩写和润色，并生成更符合读者和市场的标题和简介`;
