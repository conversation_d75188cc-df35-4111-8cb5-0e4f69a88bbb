import type { NovelGuideField } from "$lib/models/novel.model";
import <PERSON><PERSON>, { type Table } from "dexie";

export interface BookSchema {
  id?: number;
  title: string;
  summary: string;
  model?: string;
  setup?: any;
  created: Date;
  updated: Date;
}

export interface ChapterSchema {
  id?: number;
  bookId: number;
  title: string;
  summary: string;
  parentId?: number; // 支持嵌套章节
  created: Date;
  updated: Date;
}

export interface ContentSchema {
  id?: number;
  content: string;
  chapterId: number;
  updated: Date;
}

class AppDB extends Dexie {
  books!: Table<BookSchema, number>;
  chapters!: Table<ChapterSchema, number>;
  contents!: Table<ContentSchema, number>;

  constructor() {
    super('AppDatabase');
    this.version(3).stores({
      books: '++id,title',
      chapters: '++id,bookId,parentId,[bookId+parentId]',
      contents: '++id,[chapterId+updated],chapterId',
    }).upgrade(async tx => {
      const books = await tx.table('books').toArray();

      for (const entry of books) {
        // 避免重复升级
        if (!entry.updated || !entry.created) {
          entry.updated = entry.updated || new Date();
          entry.created = entry.created || entry.updated;
          await tx.table('books').put(entry);
        }
      }

      const chapters = await tx.table('chapters').toArray();

      for (const entry of chapters) {
        // 避免重复升级
        if (!entry.updated || !entry.created) {
          entry.updated = entry.updated || new Date();
          entry.created = entry.created || entry.updated;
          await tx.table('chapters').put(entry);
        }
      }
    });
  }
}

export const db = new AppDB();

export const bookDB = {
  all: async () => {
    const data = await db.books.toArray();
    return data;
  },
  get: async (id: number) => {
    const data = await db.books.where('id').equals(id).toArray();
    if (!data || 0 == data.length) throw new Error("Not Found");
    return data[0];
  },
  add: async (book: {
    title?: string,
    summary?: string,
    model?: NovelGuideField,
    setup?: any,
  }) => {
    await db.books.add({
      title: book.title ?? "未命名",
      summary: book.summary ?? "",
      model: book.model?.key,
      setup: book.setup,
      created: new Date(),
      updated: new Date(),
    });
  },
  update: async (id: number, updates: Partial<BookSchema>) => {
    updates.updated = new Date();
    await db.books.update(id, updates);
  },
  delete: async (id: number) => {
    await db.books.delete(id);
  }
}

export const chapterDB = {
  get: async (id: number) => {
    let data = await db.chapters.where('id').equals(id).toArray();
    if (!data || 0 == data.length) throw new Error("Not Found");
    return data[0];
  },
  list: async (option: { bookId?: number, parentId?: number }) => {
    let data: ChapterSchema[];
    if (option.bookId && option.parentId) {
      data = await db.chapters.where('[bookId+parentId]').equals([option.bookId, option.parentId]).toArray();
    } else if (option.bookId) {
      data = await db.chapters.where('bookId').equals(option.bookId).toArray();
    } else if (option.parentId) {
      data = await db.chapters.where('parentId').equals(option.parentId).toArray();
    } else {
      data = await db.chapters.toArray();
    }
    return data;
  },
  add: async (bookId: number, chapter: {
    id?: number,
    title?: string,
    summary?: string,
  }, parentId?: number) => {
    await db.chapters.add({
      bookId: bookId,
      title: chapter?.title ?? "未命名",
      summary: chapter?.summary ?? "",
      parentId: parentId,
      created: new Date(),
      updated: new Date(),
    });
  },
  update: async (id: number, updates: Partial<ChapterSchema>) => {
    updates.updated = new Date();
    await db.chapters.update(id, updates);
  },
  delete: async (id: number) => {
    await db.chapters.delete(id);
  }
}

export const contentDB = {
  get: async (chapterId: number) => {
    const latest = await db.contents
      .where('chapterId')
      .equals(chapterId)
      .reverse()               // 倒序
      .sortBy('updated')       // 排序字段
      .then(list => list[0]);  // 拿第一条（最新）

    return latest;
  },
  history: async (chapterId: number) => {
    let data: ContentSchema[];
    data = await db.contents.where('chapterId').equals(chapterId).toArray();
    return data;
  },
  add: async (chapterId: number, content: string) => {
    await db.contents.add({
      chapterId: chapterId,
      content: content,
      updated: new Date(),
    });
  },
  delete: async (option: {
    id?: number,
    chapterId?: number,
  }) => {
    if (option.id) {
      await db.contents.delete(option.id);
    } else if (option.chapterId) {
      await db.contents.delete(option.chapterId);
    } else {
      throw new Error("option is invalid value");
    }
  }
}
