<!-- NovelGuideItem.svelte -->
<script lang="ts">
  import { type NovelGuideField } from "$lib/models/novel.model";
  import LabelCheckboxInput from "$lib/components/common/LabelCheckboxInput.svelte";
  import LabelTextInput from "$lib/components/common/LabelTextInput.svelte";
  import LabelBaseInput from "$lib/components/common/LabelBaseInput.svelte";
  import LabelJsonInput from "$lib/components/common/LabelJSONInput.svelte";

  export let field: NovelGuideField;
  export let level;
  export let readonly = false;

  export let value: any;

  let showHelp = false;
  $: helpInfo = !field.desc || field.desc === "" ? field.name : field.desc;

  $: {
    if (field) {
      let setup: Record<string, any> | any;
      if (0 < field.fields.length) {
        setup = {};
        field.fields.forEach((f) => {
          setup[f.key] = undefined;
        });
      } else if ("string" === field.value) {
        setup = "";
      } else {
        setup = undefined;
      }
      value = field.isArray ? [setup] : setup;
    } else {
      value = undefined;
    }
  }

  // 添加一个复杂数据类型对象
  function handleAddItem() {
    let setup: any;
    if (0 < field.fields.length) {
      setup = {};
      field.fields.forEach((f) => {
        setup[f.key] = undefined;
      });
    }
    if (setup && Array.isArray(value)) {
      value = [...value, setup];
    }
  }

  function handleHelp() {
    showHelp = !showHelp;
  }
</script>

<div
  class:pl-6={level != 0 && level != 1}
  class:mt-3={level == 1}
  class:mt-2={level != 1}
>
  {#if !readonly && level != 0}
    <div class="text-lg" title={helpInfo} aria-label={helpInfo}>
      {field.name}
      <span class="text-sm">({field.key})</span>
      <button
        class=" text-xs rounded-full border px-1 hover:bg-gray-100"
        on:click={handleHelp}>?</button
      >
    </div>
    {#if showHelp}
      <div>{helpInfo}</div>
    {/if}
  {/if}
  {#if field.fields && 0 < field.fields.length}
    {#if field.isArray}
      {#each value as _, m}
        {#each field.fields as guide, i}
          <svelte:self
            field={guide}
            level={level + 1}
            readonly={false}
            bind:value={value[m][guide.key]}
          />
        {/each}
      {/each}
      <button
        class="mt-3 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-light"
        on:click={handleAddItem}>再添加一个{field.name}</button
      >
    {:else}
      {#each field.fields as guide, i}
        <svelte:self
          field={guide}
          level={level + 1}
          readonly={false}
          bind:value={value[guide.key]}
        />
      {/each}
    {/if}
  {:else if Array.isArray(field.value)}
    <LabelCheckboxInput
      class="mt-3"
      values={field.value}
      multiple={field.isArray}
      disabled={readonly}
      bind:selected={value}
    />
  {:else if "string" === field.value}
    <LabelTextInput
      class="mt-1"
      multiple={field.isArray}
      disabled={readonly}
      title={field.name}
      placeholder={helpInfo}
      bind:selected={value}
    />
  {:else if "json" === field.value}
    <LabelJsonInput
      class="mt-1"
      multiple={field.isArray}
      disabled={readonly}
      title={field.name}
      placeholder={helpInfo}
      bind:selected={value}
    />
  {:else}
    <LabelBaseInput
      class="mt-1"
      multiple={field.isArray}
      disabled={readonly}
      title={field.name}
      placeholder={helpInfo}
      type={field.value}
      bind:selected={value}
    />
  {/if}
</div>
