<!-- NovelGuideItem.svelte -->
<script lang="ts">
  import { autogrow } from "$lib/actions/autogrow";
  import LabelCheckboxInput from "$lib/components/common/LabelCheckboxInput.svelte";
  import type { NovelGuideField } from "$lib/models/novel.model";
  import NovelGuideDForm from "./NovelGuideDForm.svelte";

  export let field: NovelGuideField;
  export let value: any;

  export let level = 0;

  export let readonly = false;

  // 判断是否有叶子节点（没有子元素）
  $: isLeafNode = field.fields && 0 < field.fields.length;
  $: isStringValue = field.value === "string";
  $: isNumberValue = field.value === "number";
  $: isObjectValue = field.value === "json";
</script>

{#if isLeafNode}
  {#each field.fields as f}
    <NovelGuideDForm
      bind:field={f}
      bind:value={value[f.key as keyof any]}
      level={level + 1}
      {readonly}
    />
  {/each}
{:else if Array.isArray(field.value)}
  <LabelCheckboxInput
    class="mt-1"
    values={field.value}
    multiple={field.isArray}
    disabled={readonly}
    bind:selected={value}
  />
{:else if isStringValue}
  <textarea
    bind:value
    use:autogrow={value}
    class="w-full align-top overflow-y-hidden resize-none font-light text-lg text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors py-1"
    placeholder={field.desc}
    rows="1"
    {readonly}
  ></textarea>
{:else if isNumberValue}
  <input type="number" bind:value {readonly} />
{:else if isObjectValue}
  <input type="text" bind:value {readonly} />
{:else}
  <div>Nothing</div>
{/if}
