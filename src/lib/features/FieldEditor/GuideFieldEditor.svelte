<script lang="ts">
  import { autogrow } from "$lib/actions/autogrow";
  import AutoGenerateButton from "$lib/components/buttons/AutoGenerateButton.svelte";
  import OpenConfigButton from "$lib/components/buttons/OpenConfigButton.svelte";
  import JsonContent from "$lib/components/common/JSONContent.svelte";
  import ConfigModal from "$lib/components/modals/ConfigModal.svelte";
  import { addToast } from "$lib/components/toast/toastStore";
  import FieldEditor from "$lib/features/FieldEditor/FieldEditor.svelte";
  import {
    createNovelGuide,
    validateGuide,
    type NovelGuideField,
  } from "$lib/models/novel.model";
  import { NovelGuidePrompt } from "$lib/prompt/prompts";
  import { postAICompletion } from "$lib/services/guide.service";

  export let model: NovelGuideField;

  $: {
    if (validateGuide(model, true)) {
      model = model;
    }
  }

  let isAutoGenerate = false;
  let isPreview = false;

  // 添加新字段
  function addfield() {
    model.fields[model.fields.length] = createNovelGuide({});
    handleFieldUpdate();
  }

  // 删除字段
  function deleteField(nestedIndex: number) {
    model.fields = model.fields.filter((_, i) => nestedIndex !== i);
    handleFieldUpdate();
  }

  function moveUpField(index: number) {
    if (index > 0) {
      [model.fields[index - 1], model.fields[index]] = [
        model.fields[index],
        model.fields[index - 1],
      ];
    }
    handleFieldUpdate();
  }

  function moveDownField(index: number) {
    if (index < model.fields.length - 1) {
      [model.fields[index + 1], model.fields[index]] = [
        model.fields[index],
        model.fields[index + 1],
      ];
    }
    handleFieldUpdate();
  }

  // 导入JSON
  function importGuide(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          model = JSON.parse(e.target?.result as string);
        } catch (error) {
          alert("导入失败，请检查文件格式");
        }
      };
      reader.readAsText(file);
    }
  }

  // 处理字段更新
  function handleFieldUpdate() {
    model = model; // 触发响应式更新
  }

  function handleAICompletion() {
    isAutoGenerate = true;
    navigator.clipboard.writeText(NovelGuidePrompt + "\n" + model.desc);
    postAICompletion(model.desc.trim(), { prompt: NovelGuidePrompt })
      .then((guide) => {
        model = guide;
        if (!model.key || model.key === "") {
          throw new Error("无效的内容");
        }
        model.key = `${model.key}_${new Date().toISOString()}`;
      })
      .catch((e) => {
        model.desc = e.message;
        addToast({
          message: "一键生成失败\n" + e.message,
          type: "error",
          duration: 5000,
        });
      })
      .finally(() => {
        isAutoGenerate = false;
        handleFieldUpdate();
      });
  }
</script>

<div
  class="min-h-screen py-12 px-1 mx-auto"
  class:max-w-xl={0 == model.fields.length}
>
  <div class="flex justify-center">
    <input
      bind:value={model.name}
      on:input={handleFieldUpdate}
      placeholder="小说创作指南"
      class="text-4xl text-center max-w-full font-light text-gray-800 placeholder:text-gray-400 bg-transparent border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors px-2 py-1"
    />
  </div>
  {#if model.isArray}
    <input bind:checked={model.isArray} type="checkbox" />
  {/if}
  <div
    class="flex flex-col justify-center max-w-4xl mx-auto mt-3"
    class:mt-12={0 == model.fields.length}
  >
    {#if 0 == model.fields.length}
      <div
        class="w-full px-1 rounded-2xl shadow-2xl hover:shadow-gray-400 hover:-translate-y-1 focus-within:-translate-y-1 focus-within:shadow-gray-400 transform transition-all duration-300 ease-out bg-indigo-100 text-indigo-800 mb-2"
      >
        <textarea
          class="w-full overflow-y-hidden align-top p-2 border border-indigo-100 rounded-2xl focus:ring-0 focus:ring-blue-500 focus:border-transparent outline-none transition-all font-light resize-none"
          placeholder="你想构建一份怎样的小说创作指南？"
          rows="5"
          bind:value={model.desc}
          on:input={handleFieldUpdate}
          use:autogrow={model.desc}
        ></textarea>
        <div class="flex justify-end mx-2 my-1">
          <AutoGenerateButton
            loading={isAutoGenerate}
            disabled={!model.desc}
            text="一键完善"
            on:click={handleAICompletion}
          ></AutoGenerateButton>
        </div>
      </div>
    {:else}
      <textarea
        class="w-full align-top overflow-y-hidden resize-none text-lg font-light text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors px-2 py-1"
        placeholder="你想构建一份怎样的小说创作指南？"
        rows="1"
        bind:value={model.desc}
        on:input={handleFieldUpdate}
        use:autogrow={model.desc}
      ></textarea>
      <div class="flex justify-end">
        <AutoGenerateButton
          loading={isAutoGenerate}
          disabled={!model.desc}
          text="一键完善"
          on:click={handleAICompletion}
        ></AutoGenerateButton>
      </div>
    {/if}
  </div>

  <div class="max-w-4xl mx-auto mt-6">
    <div class="space-y-6">
      <!-- 字段编辑器 -->
      {#each model.fields as guide, i}
        <FieldEditor
          bind:field={guide}
          level={0}
          id={`${i + 1}`}
          on:delete={() => deleteField(i)}
          on:moveup={() => moveUpField(i)}
          on:movedown={() => moveDownField(i)}
          on:update={handleFieldUpdate}
        />
      {/each}
    </div>

    <!-- 操作栏 -->
    <div class="mt-16 mb-8 flex gap-3 items-center">
      <button
        on:click={addfield}
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-light"
      >
        添加字段
      </button>
      <div class="flex-1"></div>
      <button
        on:click={() => {
          isPreview = !isPreview;
        }}
        class="px-4 py-2 bg-teal-500 text-white rounded-lg hover:bg-teal-700 transition-colors font-light"
      >
        {isPreview ? "收起" : "JSON"}
      </button>
      <OpenConfigButton />
    </div>
    <div
      class="items-center gap-3 my-8 text-end"
      class:hidden={0 < model.fields.length}
    >
      <label for="file" class="text-gray-600 font-light">导入：</label>
      <input
        id="file"
        type="file"
        accept=".json"
        title="导入创作指南配置文件"
        on:change={importGuide}
        class="text-sm max-w-45 text-gray-600 file:mr-3 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-light file:bg-gray-100 file:text-gray-700 hover:file:bg-gray-200"
      />
    </div>
    <JsonContent name={model.name} bind:obj={model} bind:isShow={isPreview} />
    <ConfigModal />
  </div>
</div>
