<script lang="ts">
  import { autogrow } from "$lib/actions/autogrow";
  import { getDisplayWidth } from "$lib/utils/strings";
  import { createEventDispatcher } from "svelte";
  import {
    createNovelGuide,
    type NovelGuideField,
  } from "$lib/models/novel.model";
  import { postAICompletion } from "$lib/services/guide.service";
  import AutoGenerateButton from "$lib/components/buttons/AutoGenerateButton.svelte";
  import JsonContent from "$lib/components/common/JSONContent.svelte";
  import { NovelGuidePrompt } from "$lib/prompt/prompts";
  import unidecode from "unidecode";

  export let field: NovelGuideField;
  export let level: number = 0;
  export let isAutoGenerate = false;

  export let id = "";

  let isExpanded = true;
  let isShowJSON = false;

  const dispatch = createEventDispatcher();

  // 添加嵌套字段
  function addNestedField() {
    field.value = "string";
    field.fields[field.fields.length] = createNovelGuide({});
    updateFieldStatus();
  }

  // 删除嵌套字段
  function deleteNestedField(nestedIndex: number) {
    field.fields = field.fields.filter((_, i) => i !== nestedIndex);
    updateFieldStatus();
  }

  function moveUpField(index: number) {
    if (index > 0) {
      [field.fields[index - 1], field.fields[index]] = [
        field.fields[index],
        field.fields[index - 1],
      ];
    }
    updateFieldStatus();
  }

  function moveDownField(index: number) {
    if (index < field.fields.length - 1) {
      [field.fields[index + 1], field.fields[index]] = [
        field.fields[index],
        field.fields[index + 1],
      ];
    }
    updateFieldStatus();
  }

  // 添加数组值
  function addArrayValue(item: string) {
    if (!Array.isArray(field.value)) {
      field.value = [];
    }
    try {
      const strarr: string[] = JSON.parse(
        item.trim().replace("],", "]").replace(/'/g, '"'),
      );
      (field.value as string[]).push(...strarr);
    } catch (error) {
      (field.value as string[]).push(item);
    }

    updateFieldStatus();
  }

  function handleAddArrayValue(event: KeyboardEvent): void {
    const target = event.target as HTMLInputElement;

    if (event.key === "Enter") {
      event.preventDefault();
      addArrayValue(target.value);
      target.value = "";
    }
  }

  // 删除数组值
  function removeArrayValue(index: number) {
    if (Array.isArray(field.value)) {
      (field.value as string[]).splice(index, 1);
    }
    updateFieldStatus();
  }

  // 切换值类型
  function toggleValueType(type: "number" | "string" | "json" | "enum") {
    switch (type) {
      case "enum":
        field.value = [];
        break;
      default:
        field.value = type;
        break;
    }
    updateFieldStatus();
  }

  // 获取值类型
  function getValueType(value: string | string[]): string {
    if (Array.isArray(value)) return "enum";
    return value;
  }

  // 处理输入变化
  function updateFieldStatus() {
    dispatch("update");
  }

  function handleInputChange() {
    field.key = unidecode(field.name)
      .replaceAll("-", "")
      .replaceAll("_", "")
      .replaceAll(" ", "")
      .trim();
    updateFieldStatus();
  }

  function handleAICompletion() {
    isAutoGenerate = true;
    navigator.clipboard.writeText(NovelGuidePrompt + "\n" + field.desc);
    postAICompletion(field.desc.trim(), { prompt: NovelGuidePrompt })
      .then((guide) => {
        field = guide;
      })
      .catch((e) => {
        field.desc = e.message;
      })
      .finally(() => {
        isAutoGenerate = false;
        updateFieldStatus();
      });
  }
</script>

<div
  class="space-y-3 {level == 0
    ? ''
    : 'pl-4 pt-2 md:pl-6 md:pt-3 border-l-4 border-l-blue-200'}"
>
  <!-- 字段头部 -->
  <div class="flex flex-wrap items-center justify-between">
    <div class="flex items-center space-x-4 flex-1" {id}>
      <input
        type="text"
        bind:value={field.key}
        on:input={updateFieldStatus}
        class="w-full text-sm font-light text-gray-800 placeholder:text-gray-400 bg-transparent border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors px-2 py-1"
        placeholder="字段名"
      />
      <span class="text-gray-400 font-light shrink-0"># {id}</span>
    </div>
    <button
      on:click={() => dispatch("moveup")}
      class="transition-colors px-2 py-1 hover:bg-green-200 rounded-lg"
      title="向上移动"
      aria-label="向上移动"
    >
      ⬆️
    </button>
    <button
      on:click={() => dispatch("movedown")}
      class="transition-colors px-2 py-1 hover:bg-green-200 rounded-lg"
      title="向下移动"
      aria-label="向下移动"
    >
      ⬇️
    </button>
    <button
      on:click={() => {
        isShowJSON = !isShowJSON;
      }}
      class="transition-colors px-2 py-1 rounded-lg {isShowJSON
        ? 'bg-teal-100 hover:bg-teal-200'
        : 'hover:bg-teal-100'}"
      title="显示 JSON 格式化数据"
      aria-label="显示 JSON 格式化数据"
    >
      JSON
    </button>
    <button
      on:click={() => dispatch("delete")}
      class="text-red-500 hover:text-red-700 transition-colors p-2 hover:bg-red-50 rounded-lg"
      title="删除字段"
      aria-label="删除字段"
    >
      <svg
        class="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
        ></path>
      </svg>
    </button>
  </div>

  <!-- 基本信息 -->
  <input
    type="text"
    bind:value={field.name}
    on:input={handleInputChange}
    class="w-full text-lg text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors px-2 py-1"
    class:text-3xl={level == 0}
    class:text-lg={level != 0}
    placeholder="字段显示名称"
  />

  <textarea
    bind:value={field.desc}
    on:input={updateFieldStatus}
    use:autogrow={field.desc}
    class="w-full align-top overflow-y-hidden resize-none font-light text-lg text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors px-2 py-1"
    placeholder="字段描述"
    rows="1"
  ></textarea>

  <div class="flex justify-end">
    <AutoGenerateButton
      loading={isAutoGenerate}
      disabled={!field.desc}
      text="一键完善"
      on:click={handleAICompletion}
    ></AutoGenerateButton>
  </div>

  <!-- 值编辑 -->
  <div class="mb-6">
    <div class="flex items-center mb-3">
      <label class="flex items-center text-base m-1 mr-3">
        <input
          type="checkbox"
          bind:checked={field.isArray}
          class="h-5 w-5 border-gray-300 rounded-lg focus:ring-0 mr-1"
        />数组
      </label>
      <span class="flex-1 block text-sm font-medium text-gray-700 invisible"
        >类型
      </span>
      <div
        class="flex bg-gray-200 text-gray-700 rounded-lg"
        class:hidden={0 < field.fields.length}
      >
        <button
          on:click={() => toggleValueType("string")}
          class="px-3 py-1 text-xs transition-colors rounded-l-lg"
          class:bg-gray-600={getValueType(field.value) === "string"}
          class:text-white={getValueType(field.value) === "string"}
        >
          文本
        </button>
        <button
          on:click={() => toggleValueType("number")}
          class="px-3 py-1 text-xs transition-colors"
          class:bg-cyan-600={getValueType(field.value) === "number"}
          class:text-white={getValueType(field.value) === "number"}
        >
          数字
        </button>
        <button
          on:click={() => toggleValueType("enum")}
          class="px-3 py-1 text-xs transition-colors"
          class:bg-purple-600={getValueType(field.value) === "enum"}
          class:text-white={getValueType(field.value) === "enum"}
        >
          枚举
        </button>
        <button
          on:click={() => toggleValueType("json")}
          class="px-3 py-1 text-xs transition-colors rounded-r-lg"
          class:bg-green-600={getValueType(field.value) === "json"}
          class:text-white={getValueType(field.value) === "json"}
        >
          自定义对象
        </button>
      </div>
    </div>

    {#if Array.isArray(field.value)}
      <div class="flex flex-wrap gap-2">
        {#each field.value as item, index}
          <div
            class="inline-flex items-center rounded-full text-sm bg-indigo-100 text-indigo-800"
          >
            <input
              type="text"
              bind:value={item}
              on:input={updateFieldStatus}
              size={getDisplayWidth(item, 2)}
              class="max-w-32 px-2 py-1 border border-transparent rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all font-light"
              placeholder="枚举 {index + 1}"
            />
            <button
              title="Delete"
              aria-label="Delete"
              on:click={() => removeArrayValue(index)}
              class="px-1 py-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
        {/each}
        <div
          class="inline-flex flex-1 items-center px-1 rounded-full text-sm bg-indigo-100 text-indigo-800"
        >
          <input
            type="text"
            on:keydown={handleAddArrayValue}
            class="min-w-32 w-full px-2 py-1 border border-transparent rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all font-light"
            placeholder="新枚举项 (按回车添加)"
          />
        </div>
      </div>
    {/if}
  </div>

  <!-- 嵌套字段 -->
  {#if field.fields.length > 0}
    <div class="pt-6">
      <div class="flex items-center mb-4">
        <button
          type="button"
          on:click={() => {
            isExpanded = !isExpanded;
          }}
          class="mr-2 p-2 hover:bg-gray-200 rounded-full transition-colors duration-200"
          title="Expanded"
          aria-label="Expanded"
        >
          <svg
            class="w-5 h-5 text-gray-500 transform transition-transform duration-200"
            class:rotate-180={!isExpanded}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>
        <h4 class="text-lg font-medium text-gray-800">
          #{level + 1}
        </h4>
        <span class="flex-1 text-center">
          {#if !isExpanded}
            <span class="ml-2 font-light"
              >已折叠{field.fields.length}个字段</span
            >
          {/if}
        </span>
        <button
          on:click={addNestedField}
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-light"
        >
          添加同级字段
        </button>
      </div>

      {#if isExpanded}
        <div class="space-y-4">
          {#each field.fields as guide, i}
            <div class="overflow-hidden">
              <svelte:self
                bind:field={guide}
                level={level + 1}
                id="{id}.{i + 1}"
                on:delete={() => deleteNestedField(i)}
                on:moveup={() => moveUpField(i)}
                on:movedown={() => moveDownField(i)}
                on:update={updateFieldStatus}
              />
            </div>
          {/each}
        </div>
      {/if}
    </div>
  {:else}
    <div class="pt-6">
      <button
        on:click={addNestedField}
        class="w-full py-3 md:py-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-400 hover:text-blue-600 transition-colors font-light"
      >
        + 添加嵌套字段
      </button>
    </div>
  {/if}
  <JsonContent name={field.key} bind:obj={field} bind:isShow={isShowJSON} />
</div>
