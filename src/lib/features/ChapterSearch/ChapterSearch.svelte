<script lang="ts">
  import { onMount } from "svelte";
  import { db } from "$lib/db/db";
  import {
    searchChaptersByTitle,
    type ChapterSearchResult,
  } from "$lib/utils/fuzzySearch";
  import type { ChapterSchema } from "$lib/db/db";
  import { html } from "$lib/actions/innerHTML";

  export let bookId: number;

  let keyword = "";
  let allChapters: ChapterSchema[] = [];
  let results: ChapterSearchResult[] = [];

  onMount(async () => {
    allChapters = await db.chapters.where("bookId").equals(bookId).toArray();
  });

  $: if (keyword.length > 1) {
    results = searchChaptersByTitle(allChapters, keyword);
  } else {
    results = [];
  }

  function jumpToChapter(chapterId: number) {
    const el = document.getElementById(`chapter-${chapterId}`);
    if (el) el.scrollIntoView({ behavior: "smooth", block: "start" });
  }
</script>

<div class="space-y-2">
  <input
    type="text"
    bind:value={keyword}
    placeholder="搜索章节标题"
    class="w-full p-2 border rounded"
  />

  {#if results.length > 0}
    <ul class="divide-y">
      {#each results as { item, highlightedTitle }}
        <!-- svelte-ignore a11y_click_events_have_key_events -->
        <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
        <li
          class="py-2 cursor-pointer hover:bg-gray-100"
          on:click={() => jumpToChapter(item.id!)}
        >
          <div class="text-sm" use:html>{highlightedTitle}</div>
          <div class="text-xs text-gray-500">{item.summary}</div>
        </li>
      {/each}
    </ul>
  {/if}
</div>
