import { geminiConfig, inputText, isLoading } from "$lib/models/app-state.model";
import type { GeminiRequest } from "$lib/models/gemini.model";
import { responseText } from "$lib/stores/response.store";
import { get } from 'svelte/store';

/**
 * 从字符串中提取 ```json 和 ``` 之间的内容
 * @param input 包含JSON标记的输入字符串
 * @param rollback 回滚，如果为 true，那么解析失败时返回原文
 * @returns 提取的JSON内容字符串，如果未找到则返回null
 */
export function extractJsonContent(input: string, rollback?: boolean): string | null {
  // 使用正则表达式匹配 ```json 和 ``` 之间的内容
  const jsonBlockRegex = /```json\s*([\s\S]*?)\s*```/;
  const match = input.match(jsonBlockRegex);

  // 如果找到匹配项，返回第一个捕获组（即json内容）
  if (match && match[1]) {
    return match[1].trim(); // 去除前后空白字符
  }

  return rollback ? input : null;
}

export async function postGeminiJson<T>(inputText: string, options?: { prompt?: string, maxLenght?: number, isText?: boolean }) {
  const parts = await postGemini(inputText, options);
  const text = parts
    .map((item) => item.text)
    .join("")
    .trim();
  if (options?.isText) {
    return text;
  }
  const json = extractJsonContent(text) ?? "";
  const obj = JSON.parse(json) as T;
  return obj;
}

export async function postGemini(inputText: string, options?: { prompt?: string, maxLenght?: number }): Promise<any[]> {
  const config = get(geminiConfig);
  const text = inputText + (options?.maxLenght ? `\n请最多返回 ${options.maxLenght} 个字` : "");

  const requestBody: GeminiRequest = {
    contents: [
      {
        role: "user",
        parts: [{ text }],
      },
    ],
    generationConfig: {
      temperature: config.temperature,
      maxOutputTokens: config.maxOutputTokens,
      topP: 1,
    },
    safetySettings: [
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
    ],
  };

  if (options?.prompt) {
    requestBody.contents[0].parts.unshift({ text: options.prompt })
  }

  const resp = await fetch(
    `https://nextchat.blendiv.com/api/google/v1beta/models/${config.modelName}:generateContent`,
    {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: "Bearer " + config.apiKey.trim(),
      },
      body: JSON.stringify(requestBody),
    }
  );
  const json = await resp.json();
  if (!resp.ok) {
    throw new Error(`${resp.status} ${resp.statusText}\n${JSON.stringify(json)}`);
  }
  return await json.candidates?.[0]?.content?.parts;
}

export async function streamGeminiResponse(): Promise<void> {
  isLoading.set(true);

  const config = get(geminiConfig);
  const text = get(inputText);

  const requestBody: GeminiRequest = {
    contents: [
      {
        role: "user",
        parts: [{ text }],
      },
    ],
    generationConfig: {
      temperature: config.temperature,
      maxOutputTokens: config.maxOutputTokens,
      topP: 1,
    },
    safetySettings: [
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
    ],
  };

  try {
    const res = await fetch(
      `https://nextchat.blendiv.com/api/google/v1beta/models/${config.modelName}:streamGenerateContent?alt=sse`,
      {
        method: "POST",
        headers: {
          Accept: "text/event-stream",
          "Content-Type": "application/json, text/event-stream",
          Authorization: "Bearer " + config.apiKey.trim(),
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!res.body) {
      throw new Error("No response body received");
    }

    const reader = res.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");

      responseText.set("");

      for (const line of lines) {
        if (line.startsWith("data:")) {
          try {
            const json = JSON.parse(line.slice(5).trim());
            const parts = json.candidates?.[0]?.content?.parts ?? [];
            for (const part of parts) {
              if (part.text) {
                responseText.update((t) => t + part.text);
              }
            }
          } catch (err) {
            console.error("JSON parse error:", err, line);
          }
        }
      }
    }
  } catch (error) {
    console.error("API request failed:", error);
  } finally {
    isLoading.set(false);
  }
}

export function validateConfig(config: any): Record<string, string> {
  const errors: Record<string, string> = {};

  if (!config.apiKey?.trim()) {
    errors.apiKey = "API key 不能为空";
  }

  if (!config.modelName?.trim()) {
    errors.modelName = "模型名称不能为空";
  }

  if (config.temperature < 0 || config.temperature > 1) {
    errors.temperature = "随机性必须在0到1之间";
  }

  if (config.maxOutputTokens < 1) {
    errors.maxOutputTokens = "Token长度必须大于0";
  }

  return errors;
}