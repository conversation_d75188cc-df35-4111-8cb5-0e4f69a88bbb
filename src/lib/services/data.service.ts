import { bookDB, chapterDB, contentDB, type BookSchema, type ChapterSchema, type ContentSchema } from "$lib/db/db";
import type { NovelGuideField } from "$lib/models/novel.model";

// 数据操作结果接口
export interface DataResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// 分页参数接口
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页结果接口
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 搜索参数接口
export interface SearchParams {
  query: string;
  fields?: string[];
  fuzzy?: boolean;
}

// 书籍服务
export class BookService {
  // 获取所有书籍（带分页）
  static async getAll(pagination?: PaginationParams): Promise<DataResult<PaginatedResult<BookSchema>>> {
    try {
      const allBooks = await bookDB.all();
      
      if (!pagination) {
        return {
          success: true,
          data: {
            items: allBooks,
            total: allBooks.length,
            page: 1,
            limit: allBooks.length,
            totalPages: 1
          }
        };
      }

      const { page, limit, sortBy = 'updated', sortOrder = 'desc' } = pagination;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      // 排序
      const sortedBooks = allBooks.sort((a, b) => {
        const aValue = a[sortBy as keyof BookSchema];
        const bValue = b[sortBy as keyof BookSchema];
        
        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
        return 0;
      });

      const items = sortedBooks.slice(startIndex, endIndex);
      const totalPages = Math.ceil(allBooks.length / limit);

      return {
        success: true,
        data: {
          items,
          total: allBooks.length,
          page,
          limit,
          totalPages
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `获取书籍列表失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 根据ID获取书籍
  static async getById(id: number): Promise<DataResult<BookSchema>> {
    try {
      const book = await bookDB.get(id);
      return { success: true, data: book };
    } catch (error) {
      return {
        success: false,
        error: `获取书籍失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 搜索书籍
  static async search(searchParams: SearchParams): Promise<DataResult<BookSchema[]>> {
    try {
      const allBooks = await bookDB.all();
      const { query, fields = ['title', 'summary'], fuzzy = true } = searchParams;
      
      const filteredBooks = allBooks.filter(book => {
        return fields.some(field => {
          const value = book[field as keyof BookSchema]?.toString().toLowerCase() || '';
          const searchQuery = query.toLowerCase();
          
          if (fuzzy) {
            return value.includes(searchQuery);
          } else {
            return value === searchQuery;
          }
        });
      });

      return { success: true, data: filteredBooks };
    } catch (error) {
      return {
        success: false,
        error: `搜索书籍失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 创建书籍
  static async create(bookData: {
    title?: string;
    summary?: string;
    model?: NovelGuideField;
    setup?: any;
  }): Promise<DataResult<void>> {
    try {
      await bookDB.add(bookData);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `创建书籍失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 更新书籍
  static async update(id: number, updates: Partial<BookSchema>): Promise<DataResult<void>> {
    try {
      await bookDB.update(id, updates);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `更新书籍失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 删除书籍（级联删除相关章节和内容）
  static async delete(id: number): Promise<DataResult<void>> {
    try {
      // 获取所有相关章节
      const chapters = await chapterDB.list({ bookId: id });
      
      // 删除所有相关内容
      for (const chapter of chapters) {
        if (chapter.id) {
          await contentDB.delete({ chapterId: chapter.id });
        }
      }
      
      // 删除所有章节
      for (const chapter of chapters) {
        if (chapter.id) {
          await chapterDB.delete(chapter.id);
        }
      }
      
      // 删除书籍
      await bookDB.delete(id);
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `删除书籍失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 获取书籍统计信息
  static async getStats(id: number): Promise<DataResult<{
    chapterCount: number;
    totalWords: number;
    lastUpdated: Date;
  }>> {
    try {
      const chapters = await chapterDB.list({ bookId: id });
      let totalWords = 0;
      let lastUpdated = new Date(0);

      for (const chapter of chapters) {
        if (chapter.id) {
          const content = await contentDB.get(chapter.id);
          if (content) {
            totalWords += content.content.length;
            if (content.updated > lastUpdated) {
              lastUpdated = content.updated;
            }
          }
        }
      }

      return {
        success: true,
        data: {
          chapterCount: chapters.length,
          totalWords,
          lastUpdated
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `获取书籍统计失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
}

// 章节服务
export class ChapterService {
  // 获取书籍的章节树结构
  static async getChapterTree(bookId: number): Promise<DataResult<ChapterSchema[]>> {
    try {
      const allChapters = await chapterDB.list({ bookId });
      
      // 构建树结构
      const chapterMap = new Map<number, ChapterSchema & { children: ChapterSchema[] }>();
      const rootChapters: (ChapterSchema & { children: ChapterSchema[] })[] = [];

      // 初始化所有章节
      allChapters.forEach(chapter => {
        chapterMap.set(chapter.id!, { ...chapter, children: [] });
      });

      // 构建父子关系
      allChapters.forEach(chapter => {
        const chapterWithChildren = chapterMap.get(chapter.id!)!;
        
        if (chapter.parentId) {
          const parent = chapterMap.get(chapter.parentId);
          if (parent) {
            parent.children.push(chapterWithChildren);
          } else {
            rootChapters.push(chapterWithChildren);
          }
        } else {
          rootChapters.push(chapterWithChildren);
        }
      });

      return { success: true, data: rootChapters };
    } catch (error) {
      return {
        success: false,
        error: `获取章节树失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 移动章节（重新排序或改变父级）
  static async moveChapter(
    chapterId: number, 
    newParentId?: number, 
    newPosition?: number
  ): Promise<DataResult<void>> {
    try {
      const updates: Partial<ChapterSchema> = {};
      
      if (newParentId !== undefined) {
        updates.parentId = newParentId;
      }
      
      await chapterDB.update(chapterId, updates);
      
      // TODO: 实现位置重排序逻辑
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `移动章节失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
}

// 数据同步和备份服务
export class DataSyncService {
  // 导出所有数据
  static async exportData(): Promise<DataResult<{
    books: BookSchema[];
    chapters: ChapterSchema[];
    contents: ContentSchema[];
    exportTime: string;
    version: string;
  }>> {
    try {
      const books = await bookDB.all();
      const allChapters: ChapterSchema[] = [];
      const allContents: ContentSchema[] = [];

      // 获取所有章节和内容
      for (const book of books) {
        if (book.id) {
          const chapters = await chapterDB.list({ bookId: book.id });
          allChapters.push(...chapters);

          for (const chapter of chapters) {
            if (chapter.id) {
              const contents = await contentDB.history(chapter.id);
              allContents.push(...contents);
            }
          }
        }
      }

      return {
        success: true,
        data: {
          books,
          chapters: allChapters,
          contents: allContents,
          exportTime: new Date().toISOString(),
          version: '1.0'
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `导出数据失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 导入数据
  static async importData(data: {
    books: BookSchema[];
    chapters: ChapterSchema[];
    contents: ContentSchema[];
  }): Promise<DataResult<void>> {
    try {
      // 清空现有数据（可选）
      // await this.clearAllData();

      // 导入书籍
      for (const book of data.books) {
        await bookDB.add({
          title: book.title,
          summary: book.summary,
          model: book.model,
          setup: book.setup
        });
      }

      // 导入章节
      for (const chapter of data.chapters) {
        await chapterDB.add(chapter.bookId, {
          title: chapter.title,
          summary: chapter.summary
        }, chapter.parentId);
      }

      // 导入内容
      for (const content of data.contents) {
        await contentDB.add(content.chapterId, content.content);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `导入数据失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 清空所有数据
  static async clearAllData(): Promise<DataResult<void>> {
    try {
      const books = await bookDB.all();

      for (const book of books) {
        if (book.id) {
          await BookService.delete(book.id);
        }
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `清空数据失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 创建备份文件
  static async createBackup(): Promise<DataResult<Blob>> {
    try {
      const exportResult = await this.exportData();

      if (!exportResult.success || !exportResult.data) {
        return { success: false, error: exportResult.error };
      }

      const backupData = JSON.stringify(exportResult.data, null, 2);
      const blob = new Blob([backupData], { type: 'application/json' });

      return { success: true, data: blob };
    } catch (error) {
      return {
        success: false,
        error: `创建备份失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 从备份文件恢复
  static async restoreFromBackup(file: File): Promise<DataResult<void>> {
    try {
      const text = await file.text();
      const backupData = JSON.parse(text);

      // 验证备份数据格式
      if (!backupData.books || !backupData.chapters || !backupData.contents) {
        return { success: false, error: '备份文件格式不正确' };
      }

      return await this.importData(backupData);
    } catch (error) {
      return {
        success: false,
        error: `恢复备份失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
}

// 内容服务
export class ContentService {
  // 获取章节内容历史版本
  static async getHistory(chapterId: number): Promise<DataResult<ContentSchema[]>> {
    try {
      const history = await contentDB.history(chapterId);
      return { success: true, data: history };
    } catch (error) {
      return {
        success: false,
        error: `获取内容历史失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // 恢复到指定版本
  static async restoreVersion(chapterId: number, contentId: number): Promise<DataResult<void>> {
    try {
      const history = await contentDB.history(chapterId);
      const targetContent = history.find(c => c.id === contentId);
      
      if (!targetContent) {
        return { success: false, error: '指定的版本不存在' };
      }
      
      await contentDB.add(chapterId, targetContent.content);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `恢复版本失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
}
