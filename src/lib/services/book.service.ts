// src/lib/stores/todosStore.ts
import { bookDB, db, type BookSchema, type ChapterSchema } from '$lib/db/db';
import { type NovelGuideField } from '$lib/models/novel.model';
import { writable } from 'svelte/store';

function createBookStore() {
  const { subscribe, set } = writable<BookSchema[]>([]);

  async function refresh() {
    const data = await bookDB.all();
    set(data);
  }

  return {
    subscribe,
    refresh,
    add: async (book: {
      title?: string,
      summary?: string,
      model?: NovelGuideField,
      setup?: any,
    }) => {
      await bookDB.add(book);
    },
    update: async (id: number, updates: Partial<BookSchema>) => {
      await bookDB.update(id, updates);
      await refresh();
    },
    delete: async (id: number) => {
      await bookDB.delete(id);
      await refresh();
    }
  };
}

function createChapterStore() {
  const { subscribe, set } = writable<ChapterSchema[]>([]);

  async function refresh() {
    const data = await db.chapters.toArray();
    set(data);
  }

  return {
    subscribe,
    refresh,
    add: async (bookId: number, chapter: {
      id?: number,
      title?: string,
      summary?: string,
      content?: string,
    }, parentId?: number) => {
      await db.chapters.add({
        bookId: bookId,
        title: chapter?.title ?? "未命名",
        summary: chapter?.summary ?? "",
        content: chapter?.content ?? "",
        parentId: parentId,
      });
      await refresh();
    },
    update: async (id: number, updates: Partial<ChapterSchema>) => {
      await db.chapters.update(id, updates);
      await refresh();
    },
    delete: async (id: number) => {
      await db.chapters.delete(id);
      await refresh();
    }
  };
}

export const booksStore = createBookStore();
