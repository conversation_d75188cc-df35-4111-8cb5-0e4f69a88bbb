import { createPersistedStore } from "$lib/models/app-state.model";
import { createNovelGuide, validateGuide, type NovelGuideField } from "$lib/models/novel.model";
import { extractJsonContent, postGemini } from "./gemini.service";

export async function postAICompletion(inputText: string, options?: { prompt?: string, maxLenght?: number }) {
  try {
    const parts = await postGemini(inputText, options);
    const text = parts
      .map((item) => item.text)
      .join("")
      .trim();
    const json = extractJsonContent(text) ?? "";

    const guideFields = JSON.parse(json) as NovelGuideField;
    if (!validateGuide(guideFields, true)) {
      throw new Error("AI 格式返回错误:\n" + text);
    }

    return guideFields;
  } catch (error) {
    throw error;
  }
}

export const novelGuideModel = createPersistedStore<NovelGuideField>("novel-guide-model", createNovelGuide({}));
export const novelGuideModels = createPersistedStore<NovelGuideField[]>("novel-guide-models", []);