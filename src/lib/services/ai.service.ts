import { geminiConfig } from "$lib/models/app-state.model";
import type { GeminiRequest } from "$lib/models/gemini.model";
import { globalCache } from "$lib/utils/cache";
import { performanceMonitor } from "$lib/utils/performance";
import { get } from 'svelte/store';

// 重试配置
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2
};

// 延迟函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 计算重试延迟（指数退避）
function calculateDelay(attempt: number, config: RetryConfig): number {
  const delay = config.baseDelay * Math.pow(config.backoffFactor, attempt);
  return Math.min(delay, config.maxDelay);
}

// 生成缓存键
function generateCacheKey(input: string, options?: any): string {
  const optionsStr = options ? JSON.stringify(options) : '';
  return `ai_${btoa(input + optionsStr).slice(0, 32)}`;
}

// 从字符串中提取JSON内容
export function extractJsonContent(input: string, rollback?: boolean): string | null {
  const jsonBlockRegex = /```json\s*([\s\S]*?)\s*```/;
  const match = input.match(jsonBlockRegex);
  
  if (match && match[1]) {
    return match[1].trim();
  }
  
  return rollback ? input : null;
}

// 增强的AI请求函数
export async function callAI<T = any>(
  inputText: string, 
  options?: { 
    prompt?: string; 
    maxLength?: number; 
    isText?: boolean;
    useCache?: boolean;
    retryConfig?: Partial<RetryConfig>;
  }
): Promise<T> {
  const config = get(geminiConfig);
  const useCache = options?.useCache ?? true;
  const retryConfig = { ...defaultRetryConfig, ...options?.retryConfig };
  
  // 检查缓存
  if (useCache) {
    const cacheKey = generateCacheKey(inputText, options);
    const cached = globalCache.get(cacheKey);
    if (cached) {
      return cached;
    }
  }

  let lastError: Error = new Error('Unknown error');

  for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
    try {
      // 开始性能监控
      const performanceKey = `ai_request_${Date.now()}`;
      performanceMonitor.start(performanceKey, {
        inputLength: inputText.length,
        attempt,
        useCache
      });

      const text = inputText + (options?.maxLength ? `\n请最多返回 ${options.maxLength} 个字` : "");

      const requestBody: GeminiRequest = {
        contents: [
          {
            role: "user",
            parts: [{ text }],
          },
        ],
        generationConfig: {
          temperature: config.temperature,
          maxOutputTokens: config.maxOutputTokens,
          topP: 1,
        },
        safetySettings: [
          { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
          { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
          { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
          { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
        ],
      };

      if (options?.prompt) {
        requestBody.contents[0].parts.unshift({ text: options.prompt });
      }

      const response = await fetch(
        `https://nextchat.blendiv.com/api/google/v1beta/models/${config.modelName}:generateContent`,
        {
          method: "POST",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
            Authorization: "Bearer " + config.apiKey.trim(),
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API请求失败: ${response.status} ${response.statusText}\n${JSON.stringify(errorData)}`);
      }

      const json = await response.json();
      const parts = json.candidates?.[0]?.content?.parts;
      
      if (!parts || parts.length === 0) {
        throw new Error("AI返回了空响应");
      }

      const responseText = parts.map((item: any) => item.text).join("").trim();
      
      let result: T;
      if (options?.isText) {
        result = responseText as T;
      } else {
        const jsonContent = extractJsonContent(responseText);
        if (!jsonContent) {
          throw new Error("无法从AI响应中提取JSON内容");
        }
        result = JSON.parse(jsonContent) as T;
      }

      // 缓存结果
      if (useCache) {
        const cacheKey = generateCacheKey(inputText, options);
        globalCache.set(cacheKey, result, { ttl: 5 * 60 * 1000 }); // 5分钟缓存
      }

      // 结束性能监控
      performanceMonitor.end(performanceKey);

      return result;
      
    } catch (error) {
      lastError = error as Error;
      
      // 如果是最后一次尝试，直接抛出错误
      if (attempt === retryConfig.maxRetries) {
        break;
      }
      
      // 对于某些错误类型，不进行重试
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase();
        if (errorMessage.includes('unauthorized') || 
            errorMessage.includes('forbidden') ||
            errorMessage.includes('invalid api key')) {
          throw error;
        }
      }
      
      // 等待后重试
      const delayMs = calculateDelay(attempt, retryConfig);
      await delay(delayMs);
    }
  }
  
  throw new Error(`AI请求失败，已重试${retryConfig.maxRetries}次: ${lastError.message}`);
}

// 批量AI请求（并发控制）
export async function callAIBatch<T = any>(
  requests: Array<{ input: string; options?: any }>,
  concurrency: number = 3
): Promise<T[]> {
  const results: T[] = [];
  const errors: Error[] = [];
  
  for (let i = 0; i < requests.length; i += concurrency) {
    const batch = requests.slice(i, i + concurrency);
    const promises = batch.map(async (req, index) => {
      try {
        const result = await callAI<T>(req.input, req.options);
        return { index: i + index, result, error: null };
      } catch (error) {
        return { index: i + index, result: null, error: error as Error };
      }
    });
    
    const batchResults = await Promise.all(promises);
    
    for (const { index, result, error } of batchResults) {
      if (error) {
        errors[index] = error;
      } else {
        results[index] = result!;
      }
    }
  }
  
  if (errors.length > 0) {
    throw new Error(`批量请求中有${errors.length}个失败: ${errors.filter(Boolean).map(e => e.message).join(', ')}`);
  }
  
  return results;
}

// 清除缓存
export function clearAICache() {
  globalCache.clear();
}

// 向后兼容的函数
export const postGeminiJson = callAI;
export const postGemini = async (inputText: string, options?: any) => {
  const result = await callAI(inputText, { ...options, isText: true });
  return [{ text: result }];
};
