
/** 小说创作指南及创作所需的所有元素 */
export interface NovelGuideField {
  key: string; // 变量名
  name: string; // 变量名称（中文名）
  desc: string; // 变量描述
  value: "number" | "string" | "json" | string[]; // 该变量的类型，当 value 的值为 string[] 时，则表示该元素为枚举类型，当 fields 有值时，则忽略 value 的状态。
  fields: NovelGuideField[]; // 该变量是一个复杂数据结构
  isArray: boolean; // 该变量是否为数组
}

export interface GuideFieldValue {
  key: string;
  name: string;
  desc: string;
  isArray: boolean;
  data: any | any[];
}

export function createNovelGuide({
  key,
  name,
  desc,
  value,
  fields,
  isArray,
}: {
  key?: string;
  name?: string;
  desc?: string;
  value?: "number" | "string" | "json" | string[];
  fields?: NovelGuideField[];
  isArray?: boolean;
}): NovelGuideField {
  return {
    key: key ?? "",
    name: name ?? "",
    desc: desc ?? "",
    value: value ?? "string",
    fields: fields ?? [],
    isArray: isArray ?? false,
  }
}

/** 验证 guide 的有效性 */
export function validateGuide(guide: NovelGuideField | undefined | null, shouldComplete: boolean = false) {
  if (!guide) {
    return false;
  }
  if (!guide.fields) {
    if (shouldComplete) {
      guide.fields = [];
      return true;
    }
    return false;
  }
  for (let i = 0; i < guide.fields.length; i++) {
    const el = guide.fields[i];
    if (!validateGuide(el, shouldComplete)) return false;
  }
  return true;
}

/**
 * 将 NovelGuide 对象转换为对应的数据结构
 * @param guide NovelGuide 对象
 * @returns 转换后的数据结构
 */
export function convertNovelGuideToDataStructure(guide: NovelGuideField): any {
  let result: any;

  // 如果有 fields 子结构，优先处理复杂数据结构
  if (guide.fields && guide.fields.length > 0) {
    const obj: Record<string, any> = {};

    for (const subGuide of guide.fields) {
      obj[subGuide.key] = convertNovelGuideToDataStructure(subGuide);
    }

    result = obj;
  } else {
    // 处理基础类型
    if (guide.value !== null) {
      // 如果 value 是数组，表示枚举类型，返回第一个值作为默认值
      if (Array.isArray(guide.value)) {
        result = guide.value.length > 0 ? guide.value[0] : null;
      } else if (typeof guide.value === 'string') {
        // 如果 value 是字符串，根据类型提示返回对应的默认值
        const lowerValue = guide.value.toLowerCase();

        // 尝试推断类型
        if (lowerValue.includes('number') || lowerValue.includes('数字')) {
          result = 0;
        } else if (lowerValue.includes('boolean') || lowerValue.includes('布尔')) {
          result = false;
        } else if (lowerValue.includes('array') || lowerValue.includes('数组')) {
          result = [];
        } else if (lowerValue.includes('object') || lowerValue.includes('对象')) {
          result = {};
        } else {
          // 默认返回空字符串或提示语本身
          result = guide.value;
        }
      }
    } else {
      // 默认返回 null
      result = null;
    }
  }

  // 如果 isArray 为 true，将结果包装成数组
  if (guide.isArray) {
    return result !== null ? [result] : [];
  }

  return result;
}

/**
 * 将 NovelGuide 对象转换为 TypeScript 接口定义字符串
 * @param guide NovelGuide 对象
 * @param interfaceName 接口名称
 * @returns TypeScript 接口定义字符串
 */
export function convertNovelGuideToInterface(guide: NovelGuideField, interfaceName: string = 'GeneratedInterface'): string {
  if (!guide) return `export interface ${interfaceName} {}`;

  let result = `export interface ${interfaceName} {\n`;

  function processGuide(g: NovelGuideField, indent: string = '  '): string {
    let lines = '';

    if (g.fields && g.fields.length > 0) {
      // 复杂对象类型
      let objectType = '{\n';
      for (const subGuide of g.fields) {
        objectType += `${indent}  /** ${subGuide.desc} */\n`;
        objectType += processGuide(subGuide, indent + '  ');
      }
      objectType += `${indent}}`;

      // 如果是数组类型，添加数组标记
      if (g.isArray) {
        lines += `${indent}${g.key}: (${objectType})[];\n`;
      } else {
        lines += `${indent}${g.key}: ${objectType};\n`;
      }
    } else {
      // 基础类型
      let type = 'any';

      if (g.value !== null) {
        if (Array.isArray(g.value)) {
          // 枚举类型
          const enumValues = g.value.map(v => `'${v}'`).join(' | ');
          type = enumValues || 'string';
        } else if (typeof g.value === 'string') {
          const lowerValue = g.value.toLowerCase();
          if (lowerValue.includes('number') || lowerValue.includes('数字')) {
            type = 'number';
          } else if (lowerValue.includes('boolean') || lowerValue.includes('布尔')) {
            type = 'boolean';
          } else if (lowerValue.includes('array') || lowerValue.includes('数组')) {
            type = 'any[]';
          } else if (lowerValue.includes('object') || lowerValue.includes('对象')) {
            type = 'object';
          } else {
            type = 'string';
          }
        }
      }

      // 如果是数组类型，添加数组标记
      if (g.isArray) {
        // 如果已经是数组类型，不重复添加
        if (!type.endsWith('[]')) {
          type = `(${type})[]`;
        }
      }

      lines += `${indent}${g.key}: ${type};\n`;
    }

    return lines;
  }

  if (guide.fields && guide.fields.length > 0) {
    for (const subGuide of guide.fields) {
      result += `  /** ${subGuide.desc} */\n`;
      result += processGuide(subGuide);
    }
  } else {
    result += `  /** ${guide.desc} */\n`;
    result += processGuide(guide);
  }

  result += '}';
  return result;
}

// 使用示例
/*
const exampleGuide: NovelGuide = {
  key: 'user',
  name: '用户信息',
  desc: '用户基本信息',
  value: null,
  fields: [
    {
      key: 'name',
      name: '姓名',
      desc: '用户姓名',
      value: '请输入姓名',
      fields: [],
      isArray: false
    },
    {
      key: 'age',
      name: '年龄',
      desc: '用户年龄',
      value: 'number类型',
      fields: [],
      isArray: false
    },
    {
      key: 'hobbies',
      name: '爱好',
      desc: '用户爱好列表',
      value: '请输入爱好',
      fields: [],
      isArray: true // 爱好是数组类型
    },
    {
      key: 'gender',
      name: '性别',
      desc: '用户性别',
      value: ['男', '女', '其他'],
      fields: [],
      isArray: false
    },
    {
      key: 'profiles',
      name: '个人资料列表',
      desc: '多个个人资料',
      value: null,
      fields: [
        {
          key: 'bio',
          name: '个人简介',
          desc: '个人简介描述',
          value: '请输入个人简介',
          fields: [],
          isArray: false
        },
        {
          key: 'avatar',
          name: '头像',
          desc: '用户头像URL',
          value: 'string类型',
          fields: [],
          isArray: false
        }
      ],
      isArray: true // 个人资料是数组类型
    }
  ],
  isArray: false
};

// 转换为数据结构
const dataStructure = convertNovelGuideToDataStructure(exampleGuide);
console.log('数据结构:', JSON.stringify(dataStructure, null, 2));

// 转换为 TypeScript 接口
const interfaceStr = convertNovelGuideToInterface(exampleGuide, 'UserInfo');
console.log('TypeScript 接口:\n', interfaceStr);
*/