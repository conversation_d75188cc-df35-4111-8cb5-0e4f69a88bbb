import { writable } from "svelte/store";
import { browser } from "$app/environment";

interface GeminiConfig {
  apiKey: string;
  modelName: string;
  temperature: number;
  maxOutputTokens: number;
}

interface Novel {
  
}

// 从localStorage加载保存的配置
const loadConfig = (): GeminiConfig => {
  if (browser) {
    const saved = localStorage.getItem("gemini-config");
    return saved ? JSON.parse(saved) : {
      apiKey: "",
      modelName: "gemini-2.0-flash-lite-preview-02-05",
      temperature: 0.6,
      maxOutputTokens: 6000
    };
  }
  return {
    apiKey: "",
    modelName: "gemini-2.0-flash-lite-preview-02-05",
    temperature: 0.6,
    maxOutputTokens: 6000
  };
};

// 创建可写store并添加localStorage持久化
export const createPersistedStore = <T>(key: string, defaultValue: T) => {
  const store = writable<T>(defaultValue);

  if (browser) {
    const saved = localStorage.getItem(key);
    if (saved) store.set(JSON.parse(saved));

    store.subscribe(value => {
      localStorage.setItem(key, JSON.stringify(value));
    });
  }

  return store;
};

export const showConfigModal = writable(false);
export const geminiConfig = createPersistedStore<GeminiConfig>("gemini-config", loadConfig());
export const inputText = createPersistedStore<string>("gemini-input", "");
export const isLoading = writable(false);
export const errors = writable<Record<string, string>>({});