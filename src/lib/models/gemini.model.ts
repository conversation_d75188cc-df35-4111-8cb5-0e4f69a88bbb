export interface GeminiRequest {
  contents: {
    role: string;
    parts: { text: string }[];
  }[];
  generationConfig: {
    temperature: number;
    maxOutputTokens: number;
    topP: number;
  };
  safetySettings: {
    category: string;
    threshold: string;
  }[];
}

export interface GeminiResponse {
  candidates?: {
    content: {
      parts: {
        text: string;
      }[];
    };
  }[];
}