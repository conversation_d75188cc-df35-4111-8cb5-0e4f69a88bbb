<script lang="ts">
  import { page } from "$app/stores";
  import {
    bookDB,
    chapterDB,
    type BookSchema,
    type ChapterSchema,
  } from "$lib/db/db";
  import { onMount } from "svelte";
  import Modal from "$lib/components/modals/Modal.svelte";
  import LabelBaseInput from "$lib/components/common/LabelBaseInput.svelte";
  import LabelTextInput from "$lib/components/common/LabelTextInput.svelte";
  import { addToast } from "$lib/components/toast/toastStore";

  const id = Number($page.params.id);
  let book: BookSchema;
  let chapters: ChapterSchema[];
  let errorMessage: string;

  let isOpen = false;
  let chapterTitle = "";
  let chapterSummary = "";

  const closeModal = () => {
    isOpen = false;
  };

  const handleCreateChapter = () => {
    isOpen = true;
  };

  const handleSubmitChapter = () => {
    console.log(chapterTitle, chapterSummary);

    chapterDB
      .add(id, {
        title: chapterTitle,
        summary: chapterSummary,
      })
      .then(() => {
        refresh();
        closeModal();
        addToast({
          message: "创建成功",
          type: "success",
        });
      })
      .catch((e: any) => {
        addToast({
          message: "创建失败：\n" + e.message,
          type: "error",
        });
      });
  };

  const handleDeleteChapter = (id?: number) => {
    chapterDB
      .delete(id ?? -1)
      .then(() => {
        refresh();
        addToast({
          message: "删除成功",
          type: "success",
        });
      })
      .catch((e: any) => {
        addToast({
          message: "删除失败：\n" + e.message,
          type: "error",
        });
      });
  };

  async function refresh() {
    if (isNaN(id)) return;

    try {
      book = await bookDB.get(id);
      chapters = await chapterDB.list({ bookId: id });
    } catch (error: any) {
      errorMessage = error.message;
    }
  }

  onMount(async () => {
    refresh();
  });
</script>

<svelte:head>
  <title>蘑菇 AI 小说创作平台</title>
  <meta name="description" content="构建你的故事世界" />
  <style>
    body {
      max-width: var(--container-4xl) /* 56rem = 896px */;
      margin-inline: auto;
    }
  </style>
</svelte:head>

<div class="px-2 py-8 space-y-5">
  {#if isNaN(id)}
    <div>无效的作品 ID</div>
  {:else if book}
    <h1 class="text-3xl font-bold">{book.title}</h1>
    <p class="font-light">{book.summary}</p>
    <div class="space-y-3 flex flex-col">
      {#each chapters as chapter, i}
        <div class="flex">
          <a
            href="/chapter/{chapter.id}"
            class="flex-1 p-2 rounded-lg flex flex-row space-x-2 hover:bg-gray-100"
          >
            <span>{i + 1}.</span>
            <div>{chapter.title}</div>
          </a>
          <button
            class="p-2 rounded-lg hover:bg-red-100"
            on:click={() => handleDeleteChapter(chapter.id)}>删除</button
          >
        </div>
      {/each}
      <button on:click={handleCreateChapter}>创建一个章节</button>
    </div>
  {:else}
    <div>{errorMessage}</div>
  {/if}
</div>

<Modal class="p-6" bind:isOpen {closeModal}>
  <h2 class="text-2xl font-bold mb-4">创建一个章节</h2>
  <LabelBaseInput
    bind:selected={chapterTitle}
    multiple={false}
    type="text"
    placeholder="章节名"
  ></LabelBaseInput>

  <LabelTextInput
    bind:selected={chapterSummary}
    multiple={false}
    type="text"
    placeholder="章节简介/细纲/概要"
  ></LabelTextInput>

  <div class="flex justify-end gap-4 mt-6">
    <button
      class="px-4 py-1 border rounded hover:bg-gray-100"
      on:click={closeModal}>取消</button
    >
    <button
      class="px-4 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
      on:click={handleSubmitChapter}
      >确定
    </button>
  </div>
</Modal>
