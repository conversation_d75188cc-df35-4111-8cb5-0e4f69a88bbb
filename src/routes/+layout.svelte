<script lang="ts">
	import Sidebar from "$lib/components/sidebar/Sidebar.svelte";
	import Toast from "$lib/components/toast/Toast.svelte";
	import "../app.css";

	let { children } = $props();
</script>

<svelte:head>
	<link rel="icon" href="/favicon.png" sizes="any" />
	<link rel="apple-touch-icon" href="/favicon/web/apple-touch-icon.png" />
	<style>
		body {
			background-color: var(--color-gray-50)
				/* oklch(98.5% 0.002 247.839) = #f9fafb */;
		}
	</style>
</svelte:head>

{@render children()}

<Toast />
