<script lang="ts">
  import { goto } from "$app/navigation";
  import { autogrow } from "$lib/actions/autogrow";
  import AutoGenerateButton from "$lib/components/buttons/AutoGenerateButton.svelte";
  import LabelCheckboxInput from "$lib/components/common/LabelCheckboxInput.svelte";
  import { addToast } from "$lib/components/toast/toastStore";
  import { bookDB, type BookSchema } from "$lib/db/db";
  import AiAutoGenerate from "$lib/features/AIAutoGenerate/AIAutoGenerate.svelte";
  import NovelGuideDForm from "$lib/features/NovelGuideForm/NovelGuideDForm.svelte";
  import {
    convertNovelGuideToInterface,
    type NovelGuideField,
  } from "$lib/models/novel.model";
  import { CreateBookPrompt } from "$lib/prompt/prompts";
  import { postGeminiJson } from "$lib/services/gemini.service";
  import { novelGuideModels } from "$lib/services/guide.service";

  let bookName = "";
  let bookSummary = "";
  let bookModel: NovelGuideField;
  let bookSetup: any;

  let isAutoGenerate = false;

  $: aiPrompt = `${bookName ? "标题：" + bookName + "\n" : ""}${bookSummary ? "简介：" + bookSummary : ""}`;
  $: helpInfo =
    !bookModel || bookModel.desc === ""
      ? "这个指南很懒，什么都没写"
      : bookModel.desc;
  $: setupJson = JSON.stringify(
    bookSetup,
    (key, value) => (value === undefined ? "undefined" : value),
    2,
  );
  $: setupInterface = convertNovelGuideToInterface(bookModel);
  $: setupPrompt = `请根据输入的小说简介构建不同风格的内容`;

  function handleAICompletion() {
    isAutoGenerate = true;
    postGeminiJson<BookSchema>(aiPrompt, { prompt: CreateBookPrompt })
      .then((book: any) => {
        bookName = book.title;
        bookSummary = book.summary;
      })
      .catch((e) => {
        bookSummary = bookSummary + "\n" + e.message;
        addToast({
          message: "一键生成失败\n" + e.message,
          type: "error",
          duration: 5000,
        });
      })
      .finally(() => {
        isAutoGenerate = false;
      });
  }

  function handleStartEditor() {
    bookDB.add({
      title: bookName,
      summary: bookSummary,
      model: bookModel,
      setup: bookSetup,
    });
    goto(`/editor`);
  }

  function reset() {
    bookName = "";
    bookSummary = "";
    bookSetup = undefined;
  }
</script>

<svelte:head>
  <title>创建作品指南 —— 蘑菇 AI 小说创作平台</title>
  <meta name="description" content="构建你的故事世界" />
</svelte:head>

<div class="flex justify-center py-12">
  <div
    class="text-4xl text-center max-w-full font-light text-gray-800 bg-transparent border-0 border-b-2 border-transparent px-2 py-1"
  >
    创建作品
  </div>
</div>
<div class="flex flex-col p-2 space-y-3 max-w-4xl mx-auto">
  <label class=" text-lg">
    作品名*：<input
      bind:value={bookName}
      type="text"
      class="w-full align-top overflow-y-hidden resize-none font-light text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors py-1"
      placeholder="作品名字"
    />
  </label>
  <label class="text-lg">
    作品简介：<textarea
      class="w-full align-top overflow-y-hidden resize-none font-light text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent hover:border-gray-300 focus:border-blue-500 outline-none transition-colors py-1"
      placeholder="一个好的简介可以更好的吸引读者哦"
      rows="1"
      bind:value={bookSummary}
      use:autogrow={bookSummary}
    ></textarea>
  </label>
  <div class="flex justify-end">
    <AutoGenerateButton
      loading={isAutoGenerate}
      disabled={bookSummary.trim() === ""}
      text="一键完善"
      on:click={handleAICompletion}
    ></AutoGenerateButton>
  </div>
  <div class="text-lg">作品模板：</div>
  <LabelCheckboxInput
    values={$novelGuideModels}
    label={(item) => item.name}
    multiple={false}
    bind:selected={bookModel}
  >
    {#if bookModel}
      <a
        href="/model/{bookModel.key}/edit"
        class="inline-flex items-center px-4 py-1 rounded-full text-base transition-colors hover:bg-gray-200"
        >编辑模板
      </a>
    {/if}
  </LabelCheckboxInput>

  {#if bookModel}
    <div class="font-light">{helpInfo}</div>
    <div class="flex justify-end">
      <AiAutoGenerate
        disabled={bookSummary.trim() === ""}
        text="一键完善"
        input={bookSummary}
        datatype={setupInterface}
        prompt={setupPrompt}
        count={2}
        bind:value={bookSetup}
      ></AiAutoGenerate>
    </div>
    <!-- <pre class="flex-1 overflow-auto p-2 bg-gray-100">{JSON.stringify(
        aagtest,
        (key, value) => (value === undefined ? "undefined" : value),
        2,
      )}</pre> -->
    <div class="max-w-4xl">
      <!-- 表单内容 -->
      <NovelGuideDForm
        field={bookModel}
        bind:value={bookSetup}
        level={0}
        readonly={false}
      />
    </div>
  {/if}

  <!-- 操作按钮 -->
  <div class="flex gap-4 pt-8 mt-8 border-t border-gray-200">
    <button
      on:click={handleStartEditor}
      disabled={bookName.trim() === ""}
      type="submit"
      class="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-lg font-medium disabled:opacity-50 hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg"
    >
      开始创作
    </button>
    <button
      type="button"
      on:click={reset}
      class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200"
    >
      重置表单
    </button>
  </div>
</div>

<!-- <div class="flex flex-row">
  <pre class="flex-1 overflow-auto">{setupInterface}</pre>
  <pre class="flex-1 overflow-auto">{setupJson}</pre>
</div> -->
