<script lang="ts">
  import { browser } from "$app/environment";
  import { page } from "$app/stores";
  import { autoFocus } from "$lib/actions/autoFocus";
  import { autogrow } from "$lib/actions/autogrow";
  import { addToast } from "$lib/components/toast/toastStore";
  import {
    bookDB,
    chapterDB,
    contentDB,
    type BookSchema,
    type ChapterSchema,
    type ContentSchema,
  } from "$lib/db/db";
  import AiAutoGenerate from "$lib/features/AIAutoGenerate/AIAutoGenerate.svelte";
  import { toMarkdown } from "$lib/utils/tomd";
  import { onMount } from "svelte";

  const id = Number($page.params.id);
  let book: BookSchema;
  let chapter: ChapterSchema;
  let content: ContentSchema;
  let errorMessage: string;

  let contentText: string = "";
  let saveTimer: any;

  let prompt = "";

  $: if (contentText) {
    if (browser) {
      console.log("content text changed");

      handleContentChange();
    }
  }

  function handleContentChange() {
    // Debounce save
    if (saveTimer) clearTimeout(saveTimer);
    saveTimer = setTimeout(() => saveContent(), 5000);
  }

  function saveContent() {
    console.log("content save");

    contentDB
      .add(id, contentText)
      .then(() => {
        chapterDB.update(id, chapter);
      })
      .catch((e: any) => {
        addToast({
          message: "自动保存失败：\n" + e.message,
          type: "error",
        });
      });
  }

  onMount(async () => {
    if (isNaN(id)) return;

    try {
      chapter = await chapterDB.get(id);
      book = await bookDB.get(chapter.bookId);
      content = await contentDB.get(id);

      let pmpt = toMarkdown(book.setup, 1, `${book.title}小说设定`);

      prompt =
        pmpt +
        "\n------------------------\n请根据小说设定、故事简介和正文进行扩写和续写，并根据小说设定进行检查和修改使之符合设定，最后输出正文`";

      contentText = content.content;

      console.log(prompt);
    } catch (error: any) {
      errorMessage = error.message;
    }
  });
</script>

<svelte:head>
  <title>蘑菇 AI 小说创作平台</title>
  <meta name="description" content="构建你的故事世界" />
  <style>
    body {
      max-width: var(--container-4xl) /* 56rem = 896px */;
      margin-inline: auto;
    }
  </style>
</svelte:head>

<div class="px-2 space-y-5">
  {#if isNaN(id)}
    <div>无效的作品 ID</div>
  {:else if chapter}
    <div class="my-8 flex flex-row items-center justify-between">
      <h1 class="text-3xl font-bold">{chapter.title}</h1>
      <AiAutoGenerate
        disabled={contentText.trim() === ""}
        text="一键完善"
        {prompt}
        input={`# ${chapter.title}\n\n### 简介：${chapter.summary}\n\n${contentText}`}
        count={0}
        bind:value={contentText}
      ></AiAutoGenerate>
    </div>
    <textarea
      bind:value={contentText}
      use:autogrow={contentText}
      use:autoFocus={true}
      class="w-full min-h-dvh align-top overflow-y-hidden resize-none font-light text-lg text-gray-800 placeholder:text-gray-400 border-0 border-b-2 border-transparent outline-none transition-colors py-1"
      placeholder="作品内容"
      disabled={false}
    ></textarea>
  {:else}
    <div>{errorMessage}</div>
  {/if}
</div>
