<script lang="ts">
  import { bookDB, type BookSchema } from "$lib/db/db";
  import { onMount } from "svelte";
  import { PlusCircle, BookOpen, Sparkles, TrendingUp } from "@lucide/svelte";
  import Button from "$lib/components/ui/Button.svelte";
  import Card from "$lib/components/ui/Card.svelte";
  import Skeleton from "$lib/components/ui/Skeleton.svelte";
  import EmptyState from "$lib/components/ui/EmptyState.svelte";

  let books: BookSchema[] = [];
  let isLoading = true;

  onMount(async () => {
    try {
      books = await bookDB.all();
    } catch (error) {
      console.error("Failed to load books:", error);
    } finally {
      isLoading = false;
    }
  });

  // 统计数据
  $: totalBooks = books.length;
  $: totalWords = books.reduce((sum, book) => sum + (book.summary?.length || 0), 0);
</script>

<svelte:head>
  <title>妙笔AInovel - AI小说创作集成平台</title>
  <meta name="description" content="让每一次创作都充满无限可能 - 基于AI的一站式小说创作环境" />
  <meta name="keywords" content="AI小说创作,小说写作工具,AI写作助手,创作平台" />
</svelte:head>

<!-- 主容器 -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- 导航栏 -->
  <nav class="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <Sparkles class="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              妙笔AInovel
            </h1>
            <p class="text-xs text-gray-500">AI小说创作平台</p>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="flex items-center space-x-4">
          <a
            href="/start"
            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg"
          >
            <PlusCircle class="w-4 h-4 mr-2" />
            创建作品
          </a>
          <a
            href="/model"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <BookOpen class="w-4 h-4 mr-2" />
            模板库
          </a>
        </div>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 欢迎区域 -->
    <section class="text-center mb-12">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        让每一次创作都充满
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          无限可能
        </span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
        基于AI技术的一站式小说创作环境，从灵感激发到作品完成，我们是您最得力的创作伙伴
      </p>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <div class="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50">
          <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
            <BookOpen class="w-6 h-6 text-blue-600" />
          </div>
          <h3 class="text-2xl font-bold text-gray-900">{totalBooks}</h3>
          <p class="text-gray-600">创作作品</p>
        </div>
        <div class="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50">
          <div class="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-lg mx-auto mb-4">
            <TrendingUp class="w-6 h-6 text-indigo-600" />
          </div>
          <h3 class="text-2xl font-bold text-gray-900">{Math.floor(totalWords / 1000)}K</h3>
          <p class="text-gray-600">创作字数</p>
        </div>
        <div class="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50">
          <div class="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
            <Sparkles class="w-6 h-6 text-purple-600" />
          </div>
          <h3 class="text-2xl font-bold text-gray-900">AI</h3>
          <p class="text-gray-600">智能助手</p>
        </div>
      </div>
    </section>

    <!-- 作品展示区域 -->
    <section class="mb-12">
      <div class="flex items-center justify-between mb-8">
        <div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">我的作品</h3>
          <p class="text-gray-600">继续您的创作之旅</p>
        </div>
        {#if books.length > 0}
          <a
            href="/editor"
            class="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
          >
            查看全部 →
          </a>
        {/if}
      </div>

      {#if isLoading}
        <!-- 加载状态 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {#each Array(4) as _}
            <Card variant="glass" padding="md">
              <Skeleton height="8rem" class="mb-4" />
              <Skeleton variant="text" lines="3" />
            </Card>
          {/each}
        </div>
      {:else if books.length === 0}
        <!-- 空状态 -->
        <div class="text-center py-16">
          <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <BookOpen class="w-12 h-12 text-gray-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">还没有作品</h3>
          <p class="text-gray-600 mb-8 max-w-md mx-auto">
            开始您的第一部作品，让AI助您一臂之力，创造出精彩的故事
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/start"
              class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg"
            >
              <PlusCircle class="w-5 h-5 mr-2" />
              创建第一部作品
            </a>
            <a
              href="/model"
              class="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <BookOpen class="w-5 h-5 mr-2" />
              浏览创作模板
            </a>
          </div>
        </div>
      {:else}
        <!-- 作品网格 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {#each books as book}
            <a
              href="/book/{book.id}"
              class="group bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 ease-out"
            >
              <!-- 作品封面区域 -->
              <div class="w-full h-32 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg mb-4 flex items-center justify-center">
                <BookOpen class="w-8 h-8 text-blue-600" />
              </div>

              <!-- 作品信息 -->
              <div class="space-y-2">
                <h4 class="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
                  {book.title}
                </h4>
                <p class="text-sm text-gray-600 line-clamp-3">
                  {book.summary || "暂无简介"}
                </p>

                <!-- 作品元信息 -->
                <div class="flex items-center justify-between pt-2 text-xs text-gray-500">
                  <span>
                    {new Date(book.updated).toLocaleDateString()}
                  </span>
                  <span class="px-2 py-1 bg-blue-100 text-blue-600 rounded-full">
                    {book.model || "默认"}
                  </span>
                </div>
              </div>
            </a>
          {/each}
        </div>

        <!-- 查看更多 -->
        {#if books.length >= 8}
          <div class="text-center mt-8">
            <a
              href="/editor"
              class="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              查看全部作品
              <TrendingUp class="w-4 h-4 ml-2" />
            </a>
          </div>
        {/if}
      {/if}
    </section>
  </main>

  <!-- 页脚 -->
  <footer class="bg-white/80 backdrop-blur-md border-t border-gray-200/50 mt-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="text-center">
        <div class="flex items-center justify-center space-x-3 mb-4">
          <div class="w-6 h-6 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <Sparkles class="w-4 h-4 text-white" />
          </div>
          <span class="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            妙笔AInovel
          </span>
        </div>
        <p class="text-gray-600 mb-4">让每一次创作都充满无限可能</p>
        <p class="text-sm text-gray-500">
          © 2025 妙笔AInovel. 基于AI技术的小说创作集成平台
        </p>
      </div>
    </div>
  </footer>
</div>
