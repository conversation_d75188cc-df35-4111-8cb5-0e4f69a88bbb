<script lang="ts">
  import { bookDB, type BookSchema } from "$lib/db/db";
  import { onMount } from "svelte";

  let books: BookSchema[] = [];

  onMount(() => {
    bookDB.all().then((result) => (books = result));
  });
</script>

<svelte:head>
  <title>蘑菇 AI 小说创作平台</title>
  <meta name="description" content="构建你的故事世界" />
  <style>
    body {
      max-width: var(--container-4xl) /* 56rem = 896px */;
      margin-inline: auto;
    }
  </style>
</svelte:head>

<div class=" grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 p-3">
  {#each books as book}
    <a
      class="p-5 space-y-2 rounded-2xl shadow-sm hover:shadow-gray-400 hover:-translate-y-1 focus-within:-translate-y-1 focus-within:shadow-gray-400 transform transition-all duration-300 ease-out bg-indigo-50 text-indigo-800 mb-2"
      href="/book/{book.id}"
    >
      <h1 class="text-lg line-clamp-2">{book.title}</h1>
      <p class="line-clamp-7 text-sm">{book.summary}</p>
    </a>
  {/each}
</div>

<div class="p-2 space-y-3 flex flex-col">
  <a href="/start" class=" text-teal-500">创建一个作品</a>
  <a href="/model" class=" text-cyan-700">创作指南模板</a>
</div>
