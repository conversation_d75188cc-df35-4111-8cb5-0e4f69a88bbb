<script lang="ts">
  import { type BookSchema } from "$lib/db/db";
  import { BookService } from "$lib/services/data.service";
  import { onMount } from "svelte";
  import { PlusCircle, BookOpen, Sparkles, TrendingUp } from "@lucide/svelte";
  import Button from "$lib/components/ui/Button.svelte";
  import Card from "$lib/components/ui/Card.svelte";
  import Skeleton from "$lib/components/ui/Skeleton.svelte";
  import EmptyState from "$lib/components/ui/EmptyState.svelte";
  import Badge from "$lib/components/ui/Badge.svelte";
  import Loading from "$lib/components/ui/Loading.svelte";
  import Tooltip from "$lib/components/ui/Tooltip.svelte";
  import { addToast } from "$lib/components/toast/toastStore";

  let books: BookSchema[] = [];
  let isLoading = true;
  let error: string | null = null;

  onMount(async () => {
    const result = await BookService.getAll();

    if (result.success && result.data) {
      books = result.data.items;
      error = null;
    } else {
      console.error("Failed to load books:", result.error);
      error = result.error || "加载作品列表失败，请刷新页面重试";
      addToast({
        message: "加载作品列表失败",
        type: "error",
        duration: 5000
      });
    }

    isLoading = false;
  });

  // 统计数据
  $: totalBooks = books.length
  $: totalWords = books.reduce((sum, book) => sum + (book.summary?.length || 0), 0);
</script>

<svelte:head>
  <title>妙笔AInovel - AI小说创作集成平台</title>
  <meta name="description" content="让每一次创作都充满无限可能 - 基于AI的一站式小说创作环境" />
  <meta name="keywords" content="AI小说创作,小说写作工具,AI写作助手,创作平台" />
</svelte:head>

<!-- 主容器 -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- 导航栏 -->
  <nav class="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <Sparkles class="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              妙笔AInovel
            </h1>
            <p class="text-xs text-gray-500">AI小说创作平台</p>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="flex items-center space-x-4">
          <Button href="/start" variant="primary" size="md">
            <PlusCircle class="w-4 h-4 mr-2" />
            创建作品
          </Button>
          <Button href="/model" variant="outline" size="md">
            <BookOpen class="w-4 h-4 mr-2" />
            模板库
          </Button>
        </div>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 欢迎区域 -->
    <section class="text-center mb-12">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        让每一次创作都充满
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          无限可能
        </span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
        基于AI技术的一站式小说创作环境，从灵感激发到作品完成，我们是您最得力的创作伙伴
      </p>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <Tooltip text="您已创建的作品总数">
          <div class="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-colors duration-200">
            <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
              <BookOpen class="w-6 h-6 text-blue-600" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900">{totalBooks}</h3>
            <p class="text-gray-600">创作作品</p>
          </div>
        </Tooltip>
        <Tooltip text="所有作品的总字数统计">
          <div class="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-colors duration-200">
            <div class="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-lg mx-auto mb-4">
              <TrendingUp class="w-6 h-6 text-indigo-600" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900">{Math.floor(totalWords / 1000)}K</h3>
            <p class="text-gray-600">创作字数</p>
          </div>
        </Tooltip>
        <Tooltip text="AI助手随时为您的创作提供灵感">
          <div class="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-colors duration-200">
            <div class="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
              <Sparkles class="w-6 h-6 text-purple-600" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900">AI</h3>
            <p class="text-gray-600">智能助手</p>
          </div>
        </Tooltip>
      </div>
    </section>

    <!-- 作品展示区域 -->
    <section class="mb-12">
      <div class="flex items-center justify-between mb-8">
        <div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">我的作品</h3>
          <p class="text-gray-600">继续您的创作之旅</p>
        </div>
        {#if books.length > 0}
          <a
            href="/editor"
            class="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
          >
            查看全部 →
          </a>
        {/if}
      </div>

      {#if isLoading}
        <!-- 加载状态 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {#each Array(4) as _}
            <Card variant="glass" padding="md">
              <Skeleton height="8rem" class="mb-4" />
              <Skeleton variant="text" lines={3} />
            </Card>
          {/each}
        </div>
      {:else if error}
        <!-- 错误状态 -->
        <EmptyState
          title="加载失败"
          description={error}
        >
          {#snippet icon()}
            <svg class="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          {/snippet}

          {#snippet actions()}
            <Button onclick={() => window.location.reload()} variant="primary" size="lg">
              重新加载
            </Button>
          {/snippet}
        </EmptyState>
      {:else if books.length === 0}
        <!-- 空状态 -->
        <EmptyState
          title="还没有作品"
          description="开始您的第一部作品，让AI助您一臂之力，创造出精彩的故事"
        >
          {#snippet icon()}
            <BookOpen class="w-12 h-12 text-gray-400" />
          {/snippet}

          {#snippet actions()}
            <Button href="/start" size="lg">
              <PlusCircle class="w-5 h-5 mr-2" />
              创建第一部作品
            </Button>
            <Button href="/model" variant="outline" size="lg">
              <BookOpen class="w-5 h-5 mr-2" />
              浏览创作模板
            </Button>
          {/snippet}
        </EmptyState>
      {:else}
        <!-- 作品网格 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {#each books as book}
            <a href="/book/{book.id}" class="group">
              <Card variant="glass" padding="md" hover={true}>
                <!-- 作品封面区域 -->
                <div class="w-full h-32 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg mb-4 flex items-center justify-center">
                  <BookOpen class="w-8 h-8 text-blue-600" />
                </div>

                <!-- 作品信息 -->
                <div class="space-y-2">
                  <h4 class="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
                    {book.title}
                  </h4>
                  <p class="text-sm text-gray-600 line-clamp-3">
                    {book.summary || "暂无简介"}
                  </p>

                  <!-- 作品元信息 -->
                  <div class="flex items-center justify-between pt-2 text-xs text-gray-500">
                    <span>
                      {new Date(book.updated).toLocaleDateString()}
                    </span>
                    <Badge variant="primary" size="sm">
                      {book.model || "默认"}
                    </Badge>
                  </div>
                </div>
              </Card>
            </a>
          {/each}
        </div>

        <!-- 查看更多 -->
        {#if books.length >= 8}
          <div class="text-center mt-8">
            <Button href="/editor" variant="outline" size="lg">
              查看全部作品
              <TrendingUp class="w-4 h-4 ml-2" />
            </Button>
          </div>
        {/if}
      {/if}
    </section>
  </main>

  <!-- 页脚 -->
  <footer class="bg-white/80 backdrop-blur-md border-t border-gray-200/50 mt-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="text-center">
        <div class="flex items-center justify-center space-x-3 mb-4">
          <div class="w-6 h-6 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <Sparkles class="w-4 h-4 text-white" />
          </div>
          <span class="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            妙笔AInovel
          </span>
        </div>
        <p class="text-gray-600 mb-4">让每一次创作都充满无限可能</p>
        <p class="text-sm text-gray-500">
          © 2025 妙笔AInovel. 基于AI技术的小说创作集成平台
        </p>
      </div>
    </div>
  </footer>
</div>
