<script lang="ts">
  import OpenConfigButton from "$lib/components/buttons/OpenConfigButton.svelte";
  import StatusButton from "$lib/components/buttons/StatusButton.svelte";
  import ConfigModal from "$lib/components/modals/ConfigModal.svelte";
  import { inputText, isLoading } from "$lib/models/app-state.model";
  import { streamGeminiResponse } from "$lib/services/gemini.service";
  import { responseText } from "$lib/stores/response.store";

  async function handleStartTest() {
    $responseText = "";
    await streamGeminiResponse();
  }
</script>

<div class="px-1 py-8">
  <div class=" max-w-4xl mx-auto">
    <!-- 左侧区域 -->
    <div class="space-y-6">
      <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-800">生成式 AI 小说创作工具</h1>
        <OpenConfigButton />
      </div>

      <div class="space-y-4">
        <div class:hidden={$isLoading}>
          <label
            class="block text-base font-medium text-gray-700 mb-1"
            for="inputText"
            >小说主题（简介）
          </label>
          <textarea
            id="inputText"
            bind:value={$inputText}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[200px]"
            placeholder="输入您的问题或提示..."
          ></textarea>
        </div>

        <StatusButton
          isLoading={$isLoading}
          disabled={$isLoading || !$inputText}
          on:click={handleStartTest}
        />
      </div>
    </div>
  </div>

  <ConfigModal />
</div>
