<script lang="ts">
  import ActivityBarIcon from "$lib/components/buttons/ActivityBarIcon.svelte";
  import SwitchMode from "$lib/components/buttons/SwitchMode.svelte";
  import ResizeHandle from "$lib/components/common/ResizeHandle.svelte";

  // 布局状态
  let sidebarWidth = 250;
  let panelHeight = 200;
  let editbarWidth = 250;

  // 活动视图
  let activeSidebarView = "explorer";
  let activePanelTab = "terminal";
  let activeFile = "+page.svelte";

  // 编辑器内容
  let editorContent = `<!-- VS Code 风格编辑器 -->`;

  // 面板内容
  const panelTabs = ["terminal", "problems", "output", "debug"];

  // 功能栏图标
  const activityBarIcons = [
    { id: "explorer", icon: "folder", title: "Explorer (Ctrl+Shift+E)" },
    { id: "search", icon: "search", title: "Search (Ctrl+Shift+F)" },
    { id: "git", icon: "git-branch", title: "Source Control (Ctrl+Shift+G)" },
    { id: "debug", icon: "bug", title: "Run and Debug (Ctrl+Shift+D)" },
    { id: "extensions", icon: "puzzle", title: "Extensions (Ctrl+Shift+X)" },
  ];

  function onSidebarWidthResize(delta: number) {
    sidebarWidth = Math.max(0, Math.min(sidebarWidth + delta, 350));
  }

  function onEditbarWidthResize(delta: number) {
    editbarWidth = Math.max(0, Math.min(editbarWidth - delta, 350));
  }

  function onPanelWidthResize(delta: number) {
    panelHeight = Math.max(0, Math.min(panelHeight - delta, 350));
  }

  function handleResize() {
    if (0 == sidebarWidth) {
      sidebarWidth = 250;
    } else {
      sidebarWidth = 0;
    }
    if (0 == editbarWidth) {
      editbarWidth = 250;
    } else {
      editbarWidth = 0;
    }
    if (0 == panelHeight) {
      panelHeight = 250;
    } else {
      panelHeight = 0;
    }
  }
</script>

<svelte:head>
  <script>
    // 防止页面加载时的闪烁
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia(
        "(prefers-color-scheme: dark)",
      ).matches;
      document.documentElement.classList.toggle("dark", prefersDark);
    }
  </script>
</svelte:head>

<div
  class="flex flex-col h-screen overflow-hidden bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-200"
>
  <!-- 主内容区域 -->
  <div class="flex flex-1 overflow-hidden">
    <!-- 左侧功能栏 -->
    <div class="w-12 bg-gray-100 dark:bg-gray-800 flex flex-col items-center">
      {#each activityBarIcons as icon}
        <div class="relative w-full flex justify-center">
          {#if activeSidebarView === icon.id}
            <div
              class="absolute left-0 top-0 h-full w-1 bg-blue-600 dark:bg-white"
            ></div>
          {/if}
          <button
            class="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 w-full flex justify-center"
            class:bg-gray-200={activeSidebarView === icon.id}
            class:dark:bg-gray-700={activeSidebarView === icon.id}
            on:click={() => (activeSidebarView = icon.id)}
            title={icon.title}
          >
            <ActivityBarIcon name={icon.icon} />
          </button>
        </div>
      {/each}
      <div class="flex-1"></div>
      <div class="mt-auto w-full flex flex-col items-center">
        <button
          class="p-2 my-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 w-full flex justify-center"
          title="Manage (Ctrl+,)"
        >
          <ActivityBarIcon name="settings" />
        </button>
        <SwitchMode />
      </div>
    </div>

    <!-- 侧边栏区域 -->
    {#if activeSidebarView !== "none"}
      <div
        class="flex flex-col bg-gray-100 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"
        style="width: {sidebarWidth}px; min-width: {sidebarWidth}px;"
      >
        <!-- 侧边栏内容 -->
        {#if activeSidebarView === "explorer"}
          <div class="flex-1 overflow-y-auto">
            <div class="px-4 py-2 font-semibold text-sm">EXPLORER</div>
          </div>
        {:else if activeSidebarView === "search"}
          <div class="flex-1 overflow-y-auto">
            <div class="px-4 py-2 font-semibold text-sm">SEARCH</div>
          </div>
        {:else if activeSidebarView === "git"}
          <div class="flex-1 overflow-y-auto">
            <div class="px-4 py-2 font-semibold text-sm">SOURCE CONTROL</div>
          </div>
        {:else if activeSidebarView === "debug"}
          <div class="flex-1 overflow-y-auto">
            <div class="px-4 py-2 font-semibold text-sm">RUN AND DEBUG</div>
          </div>
        {:else if activeSidebarView === "extensions"}
          <div class="flex-1 overflow-y-auto">
            <div class="px-4 py-2 font-semibold text-sm">EXTENSIONS</div>
          </div>
        {/if}
      </div>

      <!-- 侧边栏拖动条 -->
      <ResizeHandle direction="vertical" onResize={onSidebarWidthResize} />
    {/if}

    <!-- 主编辑器区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- 编辑器标签栏 -->
      <div
        class="flex bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
      >
        {#each [activeFile] as file}
          <div
            class="px-4 py-2 text-sm border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 flex items-center"
          >
            <span>{file}</span>
            <button
              class="ml-2 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
              title="close"
              aria-label="close"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        {/each}
        <div class="flex-1 border-b border-gray-200 dark:border-gray-700"></div>
      </div>

      <!-- 编辑器内容 -->
      <div
        class="flex-1 overflow-auto bg-white dark:bg-gray-900 font-mono text-sm flex flex-row"
      >
        <textarea
          class="w-full h-full p-2 focus:outline-none bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-200"
          bind:value={editorContent}
        ></textarea>
        <!-- 编辑栏拖动条 -->
        <ResizeHandle direction="vertical" onResize={onEditbarWidthResize} />
        <div
          class="bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
          style="width: {editbarWidth}px; min-width: {editbarWidth}px;"
        >
          <div class="flex-1 overflow-y-auto">
            <div class="px-4 py-2 font-semibold text-sm">History</div>
          </div>
        </div>
      </div>

      <!-- 面板拖动条 -->
      <ResizeHandle direction="horizontal" onResize={onPanelWidthResize} />

      <!-- 底部面板 -->
      <div
        class="bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex flex-col"
        style="height: {panelHeight}px; min-height: {panelHeight}px;"
      >
        <!-- 面板标签栏 -->
        <div class="flex border-b border-gray-200 dark:border-gray-700">
          {#each panelTabs as tab}
            <button
              class="px-4 py-2 text-sm border-r border-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700 capitalize"
              class:bg-white={activePanelTab === tab}
              class:dark:bg-gray-900={activePanelTab === tab}
              on:click={() => (activePanelTab = tab)}
            >
              {tab}
            </button>
          {/each}
        </div>

        <!-- 面板内容 -->
        <div
          class="flex-1 h-full overflow-auto p-2 font-mono text-sm bg-white dark:bg-gray-900"
        >
          {#if activePanelTab === "terminal"}
            <div class="text-gray-600 dark:text-gray-400 h-full">
              <textarea
                class="w-full h-full focus:ring-0 focus:outline-none bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-200"
              ></textarea>
            </div>
          {:else if activePanelTab === "problems"}
            <div class="text-gray-600 dark:text-gray-400">
              No problems have been detected in the workspace.
            </div>
          {:else if activePanelTab === "output"}
            <div class="text-gray-600 dark:text-gray-400">
              No output to display.
            </div>
          {:else if activePanelTab === "debug"}
            <div class="text-gray-600 dark:text-gray-400">
              No active debug session.
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>

  <!-- 状态栏 -->
  <div class="flex items-center justify-between px-4 py-1 text-xs">
    <div class="flex space-x-4">
      <button on:click={handleResize}>全屏</button>
      <span class="flex items-center"> main </span>
      <span>UTF-8</span>
      <span>TypeScript</span>
    </div>
    <div class="flex space-x-4">
      <span>Ln 1, Col 1</span>
      <span>Spaces: 2</span>
      <span>UTF-8</span>
    </div>
  </div>
</div>
