<script lang="ts">
  import { novelGuideModels } from "$lib/services/guide.service";
</script>

<svelte:head>
  <title>创作指南模板 —— 蘑菇 AI 小说创作平台</title>
  <meta name="description" content="构建你的故事世界" />
  <style>
    body {
      max-width: var(--container-4xl) /* 56rem = 896px */;
      margin-inline: auto;
    }
  </style>
</svelte:head>

<div class="flex flex-col p-2 space-y-2">
  <a href="/model/new">创建一个小说创作指南模板</a>

  {#each $novelGuideModels as model}
    {#if model.name}
      <a href="/model/{model.key}/edit">{model.name}</a>
    {:else}
      <a href="/model/{model.key}/edit">未命名</a>
    {/if}
  {/each}
</div>
