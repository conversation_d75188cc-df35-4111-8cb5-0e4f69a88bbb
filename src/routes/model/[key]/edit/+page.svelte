<script lang="ts">
  import { page } from "$app/stores";
  import GuideFieldEditor from "$lib/features/FieldEditor/GuideFieldEditor.svelte";
  import { novelGuideModels } from "$lib/services/guide.service";

  let key = $page.params.key;

  let index = $novelGuideModels.findIndex((v) => v.key === key);
  let model = -1 != index ? $novelGuideModels[index] : null;

  $: {
    if (model) {
      $novelGuideModels[index] = model;
    }
  }
</script>

<svelte:head>
  <title>小说创作指南编辑器</title>
</svelte:head>

{#if model}
  <GuideFieldEditor bind:model />
{:else}
  无效的 Key
{/if}
