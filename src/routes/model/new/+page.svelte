<script lang="ts">
  import GuideFieldEditor from "$lib/features/FieldEditor/GuideFieldEditor.svelte";
  import { createNovelGuide } from "$lib/models/novel.model";
  import { novelGuideModels } from "$lib/services/guide.service";

  let model = createNovelGuide({
    key: `guide_${new Date().toISOString()}`,
  });

  $novelGuideModels[$novelGuideModels.length] = model;

  $: {
    $novelGuideModels[$novelGuideModels.length - 1] = model;
  }
</script>

<svelte:head>
  <title>小说创作指南编辑器</title>
</svelte:head>

<GuideFieldEditor bind:model />
